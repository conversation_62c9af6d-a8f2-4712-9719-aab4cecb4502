package com.project.management.users

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import com.project.management.common.entity.EntityNullableUser
import com.project.management.common.entity.ServerEntity
import com.project.management.users.UserTransactionTag
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.PreUpdate
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.io.Serializable
import java.math.BigDecimal
import java.time.ZoneOffset
import java.time.ZonedDateTime

const val USER_DELETED_FILTER = "user_deleted_filter"

@Entity
@Table(name = "users")
@FilterDef(name = USER_DELETED_FILTER, parameters = [])
@Filter(name = USER_DELETED_FILTER, condition = "deleted is NULL")
class User(
    var username: String,
    @JsonIgnore
    var password: String,
    var organizationCode: String,
    var name: String,
    var email: String,
    var phoneNumber: String,
    var secondaryPhoneNumber: String?,
    var nationalId: String,
    @JsonProperty("isAdmin")
    var isAdmin: Boolean,
    var jobTitle: String,
    var balance: BigDecimal,
    var photoUrl: String,

    override val organizationId: Long,
    override val createdBy: Long?,
    override var updatedBy: Long?
) : EntityNullableUser(), Serializable


const val BALANCE_TRANSACTION_DELETED_FILTER = "balance_transaction_deleted_filter"
@Entity(name = "balance_transactions")
@FilterDef(name = BALANCE_TRANSACTION_DELETED_FILTER, parameters = [])
@Filter(name = BALANCE_TRANSACTION_DELETED_FILTER, condition = "deleted is NULL")
class BalanceTransaction(
    var amount: BigDecimal = BigDecimal.ZERO,
    var currentAmount: BigDecimal = BigDecimal.ZERO,

    var description: String = "",
    var userId: Long = 0,
    var reference: String? = null,

    @Enumerated(EnumType.STRING)
    var transactionTag: UserTransactionTag,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override var createdByUser: User
) : ServerEntity()

enum class UserTransactionTag {
    PROJECT_EXPENSE_ADD,
    PROJECT_EXPENSE_MODIFY,
    PROJECT_EXPENSE_DELETE,

    BALANCE_ADD,
    BALANCE_WITHDRAW,
    BALANCE_MODIFY,
    BALANCE_SETTLEMENT;

    fun isProjectExpense(): Boolean {
        return this == PROJECT_EXPENSE_ADD || this == PROJECT_EXPENSE_MODIFY || this == PROJECT_EXPENSE_DELETE
    }
}