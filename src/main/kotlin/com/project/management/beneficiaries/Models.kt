package com.project.management.beneficiaries

import com.project.management.common.entity.ServerEntity
import com.project.management.users.User
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.math.BigDecimal
import java.time.ZonedDateTime

const val BENEFICIARY_DELETED_FILTER = "beneficiary_deleted_filter"
@Entity
@Table(name = "beneficiaries")
@FilterDef(name = BENEFICIARY_DELETED_FILTER, parameters = [])
@Filter(name = BENEFICIARY_DELETED_FILTER, condition = "deleted is NULL")
class Beneficiary(
    var name: String,
    var phoneNumber: String,
    var secondaryPhoneNumber: String?,

    var balanceAccumulator: BigDecimal,
    var paidAccumulator: BigDecimal,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override var createdByUser: User
) : ServerEntity()

const val BENEFICIARY_TRANSACTION_DELETED_FILTER = "beneficiary_transaction_deleted_filter"
@Entity
@Table(name = "beneficiary_transactions")
@FilterDef(name = BENEFICIARY_TRANSACTION_DELETED_FILTER, parameters = [])
@Filter(name = BENEFICIARY_TRANSACTION_DELETED_FILTER, condition = "deleted is NULL")
class BeneficiaryTransaction(
    var amount: BigDecimal,
    var amountPaid: BigDecimal,
    var description: String,
    var projectId: Long,
    var beneficiaryId: Long?,
    var transactionDate: ZonedDateTime,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override var createdByUser: User
) : ServerEntity()