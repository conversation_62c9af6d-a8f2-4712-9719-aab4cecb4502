package com.project.management.beneficiaries

import com.project.management.common.entity.validate
import com.project.management.projects.ProjectExpense
import com.project.management.projects.ProjectExpensePostRequest
import com.project.management.projects.ProjectRepository
import com.project.management.terms.TermRepository
import com.project.management.terms.TermsGroupRepository
import com.project.management.users.User
import java.time.ZonedDateTime

fun BeneficiaryPostRequest.toModel(performer: User): Beneficiary {
    return Beneficiary(
        name = name,
        phoneNumber = phoneNumber ?: phone_number!!,
        secondaryPhoneNumber = secondaryPhoneNumber ?: secondary_phone_number!!,
        balanceAccumulator = 0.0.toBigDecimal(),
        paidAccumulator = 0.0.toBigDecimal(),
        organizationId = performer.organizationId,
        createdBy = performer.id!!,
        updatedBy = performer.id!!,
        createdByUser = performer
    )
}

fun BeneficiaryTransactionPostRequest.toEntity(
    beneficiaryId: Long?,
    user: User
): BeneficiaryTransaction {
    return BeneficiaryTransaction(
        amount = amount.toBigDecimal(),
        amountPaid = amountPaid.toBigDecimal(),
        description = description,
        beneficiaryId = beneficiaryId,
        projectId = projectId,
        transactionDate = ZonedDateTime.parse(transactionDate),
        organizationId = user.organizationId,
        createdBy = user.id,
        updatedBy = user.id,
        createdByUser = user
    )
}

fun ProjectExpensePostRequest.toBeneficiaryTransaction(projectId: Long): BeneficiaryTransactionPostRequest {
    return BeneficiaryTransactionPostRequest(
        amount = amount,
        amountPaid = amountPaid,
        description = description,
        projectId = projectId,
        transactionDate = transactionDate
    )
}

internal fun ProjectExpensePostRequest.toEntity(
    projectId: Long,
    beneficiaryTransaction: BeneficiaryTransaction,
    user: User,
    version: Long
): ProjectExpense {
    return ProjectExpense(
        organizationId = user.organizationId,
        beneficiaryId = beneficiaryId,
        beneficiaryTransactionId = beneficiaryTransaction.id!!,
        termsGroupId = termsGroupId,
        termId = termId,
        projectId = projectId,
        createdBy = user.id,
        updatedBy = user.id,
        beneficiary = beneficiaryId?.let { BeneficiaryRepository.Query.getById(user, it) },
        beneficiaryTransaction = beneficiaryTransaction,
        termsGroup = termsGroupId?.let { TermsGroupRepository.Query.getById(user, it) },
        term = termId?.let { TermRepository.Query.getById(user, it) },
        createdByUser = user,
        project = projectId.let { ProjectRepository.Query.getById(user, it).validate() }
    )
}