package com.project.management.beneficiaries

import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.projects.ProjectRepository
import com.project.management.users.User

data class BeneficiaryPostRequest(
    val name: String,
    val phoneNumber: String?,
    val secondaryPhoneNumber: String?,
    val phone_number: String?, // TODO Remove
    val secondary_phone_number: String?, // TODO Remove
)

data class BeneficiaryPatchPatch(
    val name: String?,
    val phoneNumber: String?,
    val version: Long
)

data class BeneficiaryTransactionPostRequest(
    val amount: Double,
    val amountPaid: Double = amount,
    val description: String,
    val projectId: Long,
    val transactionDate: String
) {
    fun validate(user: User) {
        // Validate project (required)
        ProjectRepository.Query.getById(organizationId = user, id = projectId).validate()

        // Validate amount constraints
        if (amountPaid > amount) {
            throw BusinessException.BadRequestException(message = "Amount paid cannot be greater than amount.")
        }
    }
}

data class BeneficiaryTransactionPatchRequest(
    val description: String? = null,
    val transactionDate: String? = null,
    val version: Long
) {
    fun validate(beneficiaryTransactionId: Long, user: User): BeneficiaryTransaction {
        val transaction = BeneficiaryTransactionRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = beneficiaryTransactionId
        ).validate()
        if (transaction.version != version) throw BusinessException.ConflictException()

        return transaction
    }
}