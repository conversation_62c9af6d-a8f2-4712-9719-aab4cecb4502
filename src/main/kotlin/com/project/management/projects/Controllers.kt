package com.project.management.projects

import com.project.management.common.utility.page
import com.project.management.money.MoneyService
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/projects")
class ProjectController(
    private val projectService: ProjectService,
    private val projectExpenseService: ProjectExpenseService,
    private val projectIncomeService: ProjectIncomeService,
    private val money: MoneyService
) {
    @GetMapping
    fun getAll(): ResponseEntity<List<Project>> {
        return ResponseEntity.ok(projectService.getAll())
    }

    @PostMapping
    fun create(@RequestBody projectRequestDto: ProjectPostRequest): ResponseEntity<Project> {
        return ResponseEntity.ok(projectService.create(projectRequestDto))
    }

    @GetMapping("/{projectId}")
    fun getById(@PathVariable projectId: Long): ResponseEntity<Project> {
        return ResponseEntity.ok(projectService.getById(projectId))
    }

    @GetMapping("/{projectId}/expenses")
    fun getExpenses(
        @PathVariable projectId: Long,
        @RequestParam(required = false) limit: Int?,
        @RequestParam(required = false) offset: Int?,
        @RequestParam(required = false) sort: String?,
        @RequestParam(required = false) by: String?
    ): ResponseEntity<List<ProjectExpense>> {
        val page = page(limit, offset, sort, by)
        return ResponseEntity.ok(projectExpenseService.getAllByProjectId(projectId, page).content)
    }

    @PostMapping("/{projectId}/expenses")
    fun createExpense(
        @PathVariable projectId: Long,
        @RequestBody projectExpenseRequestDto: ProjectExpensePostRequest
    ): ResponseEntity<ProjectExpense> {
        return ResponseEntity.ok(money.projectExpenseAdd(projectExpenseRequestDto, projectId))
    }

    @GetMapping("/expenses/beneficiaries/{beneficiaryId}")
    fun getExpensesByBeneficiaryId(
        @PathVariable beneficiaryId: Long
    ): ResponseEntity<List<ProjectExpense>> {
        return ResponseEntity.ok(projectExpenseService.getAllByBeneficiaryId(beneficiaryId))
    }

    @GetMapping("/{projectId}/incomes")
    fun getIncomes(@PathVariable projectId: Long): ResponseEntity<List<ProjectIncome>> {
        return ResponseEntity.ok(projectIncomeService.getAllByProjectId(projectId))
    }

    @GetMapping("/incomes/customers/{customerId}")
    fun getIncomesByCustomerId(
        @PathVariable customerId: Long
    ): ResponseEntity<List<ProjectIncome>> {
        return ResponseEntity.ok(projectIncomeService.getAllByCustomerId(customerId))
    }

    @PostMapping("/{projectId}/incomes")
    fun createIncome(
        @PathVariable projectId: Long,
        @RequestBody projectIncomeRequestDto: ProjectIncomePostRequest
    ): ResponseEntity<ProjectIncome> {
        return ResponseEntity.ok(money.projectIncomeAdd(projectIncomeRequestDto, projectId))
    }

    @PostMapping("/{projectId}/access")
    fun addAccess(
        @PathVariable projectId: Long,
        @RequestBody body: ProjectAccessPostRequest
    ): ResponseEntity<Unit> {
        projectService.addAccess(body, projectId)
        return ResponseEntity.status(HttpStatus.CREATED).build()
    }

    @PatchMapping("/{projectId}/access")
    fun modifyAccess(
        @PathVariable projectId: Long,
        @RequestBody body: ProjectAccessPostRequest
    ): ResponseEntity<Unit> {
        projectService.modifyAccess(body, projectId)
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }

    @DeleteMapping("/{projectId}/access")
    fun removeAccess(
        @PathVariable projectId: Long,
        @RequestBody body: Map<String, Any>
    ): ResponseEntity<Unit> {
        projectService.removeAccess(projectId, (body["userId"] as Integer).toLong())
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }

    @PatchMapping("/{projectId}")
    fun editProject(
        @PathVariable projectId: Long,
        @RequestBody body: ProjectPatchRequest
    ): ResponseEntity<Project> {
        return ResponseEntity.ok(projectService.editProject(body, projectId))
    }

    @PostMapping("/{projectId}/incomes/{projectIncomeId}/amount")
    fun modifyIncomeAmount(
        @PathVariable projectIncomeId: Long,
        @RequestBody request: ProjectIncomeAmountPatchRequest
    ): ResponseEntity<ProjectIncome> {
        return ResponseEntity.ok(money.projectIncomeUpdate(request, projectIncomeId))
    }

    @DeleteMapping("/{projectId}/incomes/{projectIncomeId}")
    fun deleteIncome(
        @PathVariable projectIncomeId: Long,
        @PathVariable projectId: Long
    ): ResponseEntity<Unit> {
        money.projectIncomeDelete(projectIncomeId)
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }

    @PostMapping("/{projectId}/expenses/{projectExpenseId}/amount")
    fun modifyExpenseAmount(
        @PathVariable projectExpenseId: Long,
        @RequestBody request: ProjectExpenseAmountPatchRequest
    ): ResponseEntity<ProjectExpense> {
        return ResponseEntity.ok(money.projectExpenseUpdate(request, projectExpenseId))
    }

    @DeleteMapping("/{projectId}/expenses/{projectExpenseId}")
    fun deleteExpense(
        @PathVariable projectExpenseId: Long,
        @PathVariable projectId: Long
    ): ResponseEntity<Unit> {
        money.projectExpenseDelete(projectExpenseId)
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }

    @PatchMapping("/{projectId}/expenses/{projectExpenseId}")
    fun modifyExpense(
        @PathVariable projectExpenseId: Long,
        @RequestBody request: ProjectExpensePatchRequest
    ): ResponseEntity<ProjectExpense> {
        // For now, delegate to money service for complex expense updates
        // This would need a dedicated service method for full expense updates
        return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).build()
    }

    @DeleteMapping("/{projectId}")
    fun deleteProject(
        @PathVariable projectId: Long
    ): ResponseEntity<Unit> {
        projectService.delete(projectId)
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }
}