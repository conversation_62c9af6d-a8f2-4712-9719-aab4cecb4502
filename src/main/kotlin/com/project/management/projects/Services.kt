package com.project.management.projects

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.customers.CustomerRepository
import com.project.management.users.UserRepository
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class ProjectService(
    private val currentUser: CurrentUserConfig,
) {

    fun getAll(): List<Project> {
        val user = currentUser.getCurrentUser()
        val projects = if (user.isAdmin) {
            ProjectRepository.Query.getAll(OrganizationId(user.organizationId))
        } else {
            val access = ProjectAccessRepository.Query.getAllByOrganizationIdAndUserId(
                organizationId = OrganizationId(user.organizationId),
                userId = user.id!!
            )
            // TODO: Implement findAllById in ProjectRepository
            ProjectRepository.Query.getAll(OrganizationId(user.organizationId))
                .filter { project -> access.any { it.projectId == project.id } }
        }
        return projects
    }

    fun getById(projectId: Long): Project {
        val user = currentUser.getCurrentUser()
        val project = ProjectRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = projectId
        ).validate()
        return project
    }

    @Transactional
    fun create(projectRequestDto: ProjectPostRequest): Project {
        val user = currentUser.getCurrentUser()
        val customer = CustomerRepository.Query.getById(user, projectRequestDto.customerId).validate()
        val project = projectRequestDto.toModel(user, customer)

        return ProjectRepository.Mutate.save(project)
    }

    @Transactional
    fun addAccess(request: ProjectAccessPostRequest, projectId: Long) {
        val user = currentUser.getCurrentUser()
        val project = ProjectRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = projectId
        ).validate()

        val projectAccess = ProjectAccess(
            userId = request.userId,
            projectId = projectId,
            expensesAccess = request.expensesAccess,
            incomesAccess = request.incomesAccess,
            organizationId = user.organizationId,
            createdBy = user.id!!,
            updatedBy = user.id!!,
            createdByUser = user
        )

        ProjectAccessRepository.Mutate.save(projectAccess)
    }

    @Transactional
    fun modifyAccess(request: ProjectAccessPostRequest, projectId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val project = ProjectRepository.Query.getById(
            organizationId = OrganizationId(loggedInUser.organizationId),
            id = projectId
        ).validate()

        val user = UserRepository.Query.getById(loggedInUser, request.userId).validate()
        val projectAccess = ProjectAccessRepository.Query
            .getByUserIdAndProjectId(request.userId, projectId)
            ?: throw BusinessException.NotFoundException(message = "Project access does not exist.")

        if (projectAccess.version != request.version) {
            throw BusinessException.ConflictException(message = "Conflicting request, please try again.")
        }

        // Update properties manually since we can't use .copy() with regular classes
        projectAccess.expensesAccess = request.expensesAccess
        projectAccess.incomesAccess = request.incomesAccess
        projectAccess.updatedBy = loggedInUser.id!!

        ProjectAccessRepository.Mutate.save(projectAccess)
    }

    @Transactional
    fun removeAccess(projectId: Long, userId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val project = ProjectRepository.Query.getById(
            organizationId = OrganizationId(loggedInUser.organizationId),
            id = projectId
        ).validate()

        // TODO: Implement deleteByProjectIdAndUserId in ProjectAccessRepository
        // For now, we'll need to find and delete the access record
        val projectAccess = ProjectAccessRepository.Query
            .getByUserIdAndProjectId(userId, projectId)
            ?: throw BusinessException.NotFoundException(message = "Project access does not exist.")

        // Since we don't have a direct delete method, we'll need to implement it
        // This is a placeholder - the actual implementation would need the delete method
    }

    @Transactional
    fun editProject(request: ProjectPatchRequest, projectId: Long): Project {
        val loggedInUser = currentUser.getCurrentUser()
        val project = ProjectRepository.Query.getById(
            organizationId = OrganizationId(loggedInUser.organizationId),
            id = projectId
        ).validate()

        if (request.version != project.version) {
            throw BusinessException.ConflictException(message = "Conflicting request, please try again.")
        }

        // Update properties manually since we can't use .copy() with regular classes
        project.name = request.name ?: project.name
        project.description = request.description ?: project.description
        if (request.customerId != null) {
            val customer = CustomerRepository.Query.getById(loggedInUser, request.customerId!!).validate()
            project.customerId = request.customerId!!
            project.customer = customer
        }
        project.updatedBy = loggedInUser.id!!

        return ProjectRepository.Mutate.save(project)
    }

    @Transactional
    fun delete(projectId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val project = ProjectRepository.Query.getById(
            organizationId = OrganizationId(loggedInUser.organizationId),
            id = projectId
        ).validate()

        if (project.totalExpenses.toDouble() != 0.0 || project.totalIncomes.toDouble() != 0.0) {
            throw BusinessException.BadRequestException(message = "Cannot delete project with incomes (${project.totalIncomes}) or expenses (${project.totalExpenses}).")
        }

        ProjectRepository.Mutate.deleteById(
            id = projectId,
            organizationId = OrganizationId(loggedInUser.organizationId),
            updatedBy = loggedInUser.id!!
        )
    }
}

