package com.project.management.projects

import com.project.management.beneficiaries.BeneficiaryTransactionPostRequest
import com.project.management.beneficiaries.BeneficiaryTransaction
import com.project.management.customers.Customer
import com.project.management.customers.CustomerTransaction
import com.project.management.customers.CustomerTransactionPostRequest
import com.project.management.users.User
import java.math.BigDecimal

fun ProjectPostRequest.toModel(performer: User, customer: Customer): Project {
    return Project(
        name = name,
        description = description,
        totalExpenses = BigDecimal.ZERO,
        totalPaidExpenses = BigDecimal.ZERO,
        totalIncomes = BigDecimal.ZERO,
        totalPaidIncomes = BigDecimal.ZERO,
        isActive = isActive,
        customerId = customerId,
        customer = customer,
        organizationId = performer.organizationId,
        createdBy = performer.id!!,
        updatedBy = performer.id!!,
        createdByUser = performer
    )
}

fun ProjectExpensePostRequest.toBeneficiaryTransactionRequest(projectId: Long): BeneficiaryTransactionPostRequest {
    return BeneficiaryTransactionPostRequest(
        amount = amount,
        amountPaid = amountPaid,
        description = description,
        projectId = projectId,
        transactionDate = transactionDate
    )
}

fun ProjectIncomePostRequest.toCustomerTransactionRequest(projectId: Long): CustomerTransactionPostRequest {
    return CustomerTransactionPostRequest(
        amount = amount,
        amountPaid = amountPaid,
        description = description,
        projectId = projectId,
        transactionDate = transactionDate
    )
}

fun ProjectExpensePostRequest.toModel(
    projectId: Long,
    beneficiaryTransaction: BeneficiaryTransaction,
    user: User
): ProjectExpense {
    return ProjectExpense(
        beneficiaryId = beneficiaryId,
        beneficiaryTransactionId = beneficiaryTransaction.id!!,
        termsGroupId = termsGroupId,
        termId = termId,
        projectId = projectId,
        beneficiary = null, // Will be loaded lazily
        beneficiaryTransaction = beneficiaryTransaction,
        termsGroup = null, // Will be loaded lazily
        term = null, // Will be loaded lazily
        project = null!!, // Will be loaded lazily
        organizationId = user.organizationId,
        createdBy = user.id!!,
        updatedBy = user.id!!,
        createdByUser = user
    )
}

fun ProjectIncomePostRequest.toModel(
    projectId: Long,
    customerTransaction: CustomerTransaction,
    customer: Customer,
    project: Project,
    user: User
): ProjectIncome {
    return ProjectIncome(
        customerId = customerId,
        customerTransactionId = customerTransaction.id!!,
        projectId = projectId,
        customer = customer,
        customerTransaction = customerTransaction,
        project = project,
        organizationId = user.organizationId,
        createdBy = user.id!!,
        updatedBy = user.id!!,
        createdByUser = user
    )
}