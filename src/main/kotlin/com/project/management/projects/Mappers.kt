package com.project.management.projects

import com.project.management.beneficiaries.BeneficiaryTransactionPostRequest
import com.project.management.customers.Customer
import com.project.management.customers.CustomerTransaction
import com.project.management.customers.CustomerTransactionPostRequest
import com.project.management.users.User
import java.math.BigDecimal

fun ProjectPostRequest.toModel(performer: User, customer: Customer): Project {
    return Project(
        name = name,
        description = description,
        totalExpenses = BigDecimal.ZERO,
        totalPaidExpenses = BigDecimal.ZERO,
        totalIncomes = BigDecimal.ZERO,
        totalPaidIncomes = BigDecimal.ZERO,
        isActive = isActive,
        customerId = customerId,
        version = 1,
        customer = customer,
        organizationId = performer.organizationId,
        createdBy = performer.id!!,
        updatedBy = performer.id!!,
        createdByUser = performer
    )
}

fun ProjectExpensePostRequest.toBeneficiaryTransactionRequest(projectId: Long): BeneficiaryTransactionPostRequest {
    return BeneficiaryTransactionPostRequest(
        amount = amount,
        amountPaid = amountPaid,
        description = description,
        projectId = projectId,
        transactionDate = transactionDate
    )
}

fun ProjectIncomePostRequest.toCustomerTransactionRequest(projectId: Long): CustomerTransactionPostRequest {
    return CustomerTransactionPostRequest(
        amount = amount,
        amountPaid = amountPaid,
        description = description,
        projectId = projectId,
        transactionDate = transactionDate
    )
}

fun ProjectIncomePostRequest.toModel(
    performer: User,
    customer: Customer,
    project: Project,
    customerTransaction: CustomerTransaction,
    projectId: Long
): ProjectIncome {
    return ProjectIncome(
        customerId = customerId,
        customerTransactionId = customerTransaction.id!!,
        projectId = projectId,
        version = 1,
        customer = customer,
        customerTransaction = customerTransaction,
        project = project,
        organizationId = performer.organizationId,
        createdBy = performer.id!!,
        updatedBy = performer.id!!,
        createdByUser = performer
    )
}