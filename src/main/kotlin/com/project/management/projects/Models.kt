package com.project.management.projects

import com.project.management.beneficiaries.Beneficiary
import com.project.management.beneficiaries.BeneficiaryTransaction
import com.project.management.common.entity.ServerEntity
import com.project.management.customers.Customer
import com.project.management.customers.CustomerTransaction
import com.project.management.terms.Term
import com.project.management.terms.TermsGroup
import com.project.management.users.User
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.math.BigDecimal
import java.time.ZonedDateTime

const val PROJECT_DELETED_FILTER = "project_deleted_filter"
@Entity
@Table(name = "projects")
@FilterDef(name = PROJECT_DELETED_FILTER, parameters = [])
@Filter(name = PROJECT_DELETED_FILTER, condition = "deleted is NULL")
class Project(
    var name: String,
    var description: String,
    var totalExpenses: BigDecimal,
    var totalPaidExpenses: BigDecimal,
    var totalIncomes: BigDecimal,
    var totalPaidIncomes: BigDecimal,
    var isActive: Boolean,
    @Column(name = "customer_id")
    val customerId: Long,
    var version: Long,

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", updatable = false, insertable = false)
    val customer: Customer,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override val createdByUser: User
) : ServerEntity()

const val PROJECT_EXPENSE_DELETED_FILTER = "project_expense_deleted_filter"
@Entity
@Table(name = "project_expenses")
@FilterDef(name = PROJECT_EXPENSE_DELETED_FILTER, parameters = [])
@Filter(name = PROJECT_EXPENSE_DELETED_FILTER, condition = "deleted is NULL")
class ProjectExpense(
    @Column(name = "beneficiary_id")
    var beneficiaryId: Long?,
    @Column(name = "beneficiary_transaction_id")
    val beneficiaryTransactionId: Long,
    @Column(name = "terms_group_id")
    var termsGroupId: Long?,
    @Column(name = "term_id")
    var termId: Long?,
    @Column(name = "project_id")
    var projectId: Long,
    var version: Long,

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "beneficiary_id", updatable = false, insertable = false)
    val beneficiary: Beneficiary?,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "beneficiary_transaction_id", updatable = false, insertable = false)
    val beneficiaryTransaction: BeneficiaryTransaction,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "terms_group_id", updatable = false, insertable = false)
    val termsGroup: TermsGroup?,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "term_id", updatable = false, insertable = false)
    val term: Term?,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", updatable = false, insertable = false)
    val project: Project,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override val createdByUser: User
) : ServerEntity()

const val PROJECT_EXPENSE_ENTITY_DELETED_FILTER = "project_expense_entity_deleted_filter"
@Entity
@Table(name = "project_expenses")
@FilterDef(name = PROJECT_EXPENSE_ENTITY_DELETED_FILTER, parameters = [])
@Filter(name = PROJECT_EXPENSE_ENTITY_DELETED_FILTER, condition = "deleted is NULL")
class ProjectExpenseEntity(
    @Column(name = "beneficiary_id")
    val beneficiaryId: Long?,
    @Column(name = "beneficiary_transaction_id")
    val beneficiaryTransactionId: Long,
    @Column(name = "terms_group_id")
    val termsGroupId: Long?,
    @Column(name = "term_id")
    val termId: Long?,
    @Column(name = "project_id")
    val projectId: Long,
    var version: Long,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override val createdByUser: User
) : ServerEntity()

const val PROJECT_INCOME_DELETED_FILTER = "project_income_deleted_filter"
@Entity
@Table(name = "project_incomes")
@FilterDef(name = PROJECT_INCOME_DELETED_FILTER, parameters = [])
@Filter(name = PROJECT_INCOME_DELETED_FILTER, condition = "deleted is NULL")
class ProjectIncome(
    @Column(name = "customer_id")
    val customerId: Long,
    @Column(name = "customer_transaction_id")
    val customerTransactionId: Long,
    @Column(name = "project_id")
    val projectId: Long,
    var version: Long,

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", updatable = false, insertable = false)
    val customer: Customer,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_transaction_id", updatable = false, insertable = false)
    val customerTransaction: CustomerTransaction,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", updatable = false, insertable = false)
    val project: Project,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override val createdByUser: User
) : ServerEntity()

const val PROJECT_ACCESS_DELETED_FILTER = "project_access_deleted_filter"
@Entity
@Table(name = "projects_access")
@FilterDef(name = PROJECT_ACCESS_DELETED_FILTER, parameters = [])
@Filter(name = PROJECT_ACCESS_DELETED_FILTER, condition = "deleted is NULL")
class ProjectAccess(
    val userId: Long,
    val projectId: Long,
    @Column(name = "expenses_access")
    val expensesAccess: Boolean,
    val incomesAccess: Boolean,
    var version: Long,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override val createdByUser: User
) : ServerEntity()

data class UserProjectAccess(
    val user: User,
    val projectAccess: ProjectAccess
)