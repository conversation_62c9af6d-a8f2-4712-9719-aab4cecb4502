package com.project.management.projects

import com.project.management.common.entity.OrganizationId
import com.project.management.common.utility.autowired
import com.project.management.common.utility.filterDeleted
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

object ProjectRepository {
    private val repo: SpringProjectRepository by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<Project> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getById(organizationId: OrganizationId, id: Long): Project? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }

        // TODO secure with organization id
        fun getAllByIds(organizationId: OrganizationId, ids: List<Long>): List<Project> = filterDeleted {
            repo.findAllById(ids)
        }

        fun sumTotalExpenses(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumTotalExpensesByOrganizationId(organizationId.value)
        }

        fun sumTotalPaidExpenses(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumTotalPaidExpensesByOrganizationId(organizationId.value)
        }

        fun sumTotalIncomes(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumTotalIncomesByOrganizationId(organizationId.value)
        }

        fun sumTotalPaidIncomes(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumTotalPaidIncomesByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(project: Project): Project {
            return repo.save(project)
        }

        fun deleteById(id: Long, organizationId: OrganizationId, updatedBy: Long) {
            repo.deleteByIdAndOrganizationId(id, organizationId.value, updatedBy)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(PROJECT_DELETED_FILTER, block)
}

object ProjectExpenseRepository {
    private val queryRepo: SpringProjectExpenseQueryRepository by autowired()
    private val mutateRepo: SpringProjectExpenseMutateRepository by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<ProjectExpense> = filterDeleted {
            queryRepo.findAllByOrganizationId(organizationId.value)
        }

        fun getAllByProjectId(
            organizationId: OrganizationId,
            projectId: Long,
            pageable: Pageable
        ): Page<ProjectExpense> = filterDeleted {
            queryRepo.findAllByOrganizationIdAndProjectId(organizationId.value, projectId, pageable)
        }

        fun getAllByBeneficiaryId(
            organizationId: OrganizationId,
            beneficiaryId: Long
        ): List<ProjectExpense> = filterDeleted {
            queryRepo.findAllByOrganizationIdAndBeneficiaryId(organizationId.value, beneficiaryId)
        }

        fun getById(organizationId: OrganizationId, id: Long): ProjectExpense? = filterDeleted {
            queryRepo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun getAllCreatedLastWeek(organizationId: OrganizationId): List<ProjectExpense> = filterDeleted {
            queryRepo.findAllByOrganizationIdCreatedAtLastWeek(organizationId.value)
        }

        fun getAllCreatedThisWeek(organizationId: OrganizationId): List<ProjectExpense> = filterDeleted {
            queryRepo.findAllByOrganizationIdCreatedThisWeek(organizationId.value)
        }

        fun sumAmount(organizationId: OrganizationId): BigDecimal = filterDeleted {
            queryRepo.sumAmountByOrganizationId(organizationId.value)
        }

        fun sumAmountPaid(organizationId: OrganizationId): BigDecimal = filterDeleted {
            queryRepo.sumAmountPaidByOrganizationId(organizationId.value)
        }

        fun sumAmountCreatedThisWeek(organizationId: OrganizationId): BigDecimal = filterDeleted {
            queryRepo.sumAmountByOrganizationIdCreatedThisWeek(organizationId.value)
        }
    }

    object Mutate {
        fun save(expense: ProjectExpense): ProjectExpense {
            return queryRepo.save(expense)
        }

        fun deleteById(id: Long, organizationId: OrganizationId, updatedBy: Long) {
            mutateRepo.deleteByIdAndOrganizationId(id, organizationId.value, updatedBy)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(PROJECT_EXPENSE_DELETED_FILTER, block)
}

object ProjectIncomeRepository {
    private val repo: SpringProjectIncomeRepository by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<ProjectIncome> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getAllByProjectId(
            organizationId: OrganizationId,
            projectId: Long
        ): List<ProjectIncome> = filterDeleted {
            repo.findAllByOrganizationIdAndProjectId(organizationId.value, projectId)
        }

        fun getAllByCustomerId(
            organizationId: OrganizationId,
            customerId: Long
        ): List<ProjectIncome> = filterDeleted {
            repo.findAllByOrganizationIdAndCustomerId(organizationId.value, customerId)
        }

        fun getById(organizationId: OrganizationId, id: Long): ProjectIncome? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun getByCustomerTransactionId(
            organizationId: OrganizationId,
            customerTransactionId: Long
        ): ProjectIncome? = filterDeleted {
            repo.findByCustomerTransactionIdAndOrganizationId(organizationId.value, customerTransactionId)
        }

        fun getAllCreatedLastWeek(organizationId: OrganizationId): List<ProjectIncome> = filterDeleted {
            repo.findAllByOrganizationIdCreatedLastWeek(organizationId.value)
        }

        fun getAllCreatedThisWeek(organizationId: OrganizationId): List<ProjectIncome> = filterDeleted {
            repo.findAllByOrganizationIdCreatedThisWeek(organizationId.value)
        }

        fun sumAmount(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumAmountByOrganizationId(organizationId.value)
        }

        fun sumAmountPaid(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumAmountPaidByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(income: ProjectIncome): ProjectIncome {
            return repo.save(income)
        }

        fun deleteById(id: Long, organizationId: OrganizationId, updatedBy: Long) {
            repo.deleteByIdAndOrganizationId(id, organizationId.value, updatedBy)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(PROJECT_INCOME_DELETED_FILTER, block)
}

object ProjectAccessRepository {
    private val repo: SpringProjectAccessRepository by autowired()

    object Query {
        fun getAllByOrganizationIdAndUserId(
            organizationId: OrganizationId,
            userId: Long
        ): List<ProjectAccess> = filterDeleted {
            repo.findAllByOrganizationIdAndUserId(organizationId.value, userId)
        }

        fun getAllByOrganizationIdAndProjectId(
            organizationId: OrganizationId,
            projectId: Long
        ): List<ProjectAccess> = filterDeleted {
            repo.findAllByOrganizationIdAndProjectId(organizationId.value, projectId)
        }

        fun getByUserIdAndProjectId(userId: Long, projectId: Long): ProjectAccess? = filterDeleted {
            repo.findByUserIdAndProjectId(userId, projectId)
        }
    }

    object Mutate {
        fun save(access: ProjectAccess): ProjectAccess {
            return repo.save(access)
        }

        fun deleteByProjectIdAndUserId(projectId: Long, userId: Long, updatedBy: Long) {
            repo.deleteByProjectId(projectId, userId, updatedBy)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(PROJECT_ACCESS_DELETED_FILTER, block)
}

@Repository
private interface SpringProjectRepository : JpaRepository<Project, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<Project>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): Project?

    @Modifying
    @Query(
        value = "UPDATE projects SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)

    // SUM queries for project aggregation
    @Query(
        value = "SELECT COALESCE(SUM(total_expenses), 0) FROM projects WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumTotalExpensesByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(total_paid_expenses), 0) FROM projects WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumTotalPaidExpensesByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(total_incomes), 0) FROM projects WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumTotalIncomesByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(total_paid_incomes), 0) FROM projects WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumTotalPaidIncomesByOrganizationId(organizationId: Long): BigDecimal
}

@Repository
private interface SpringProjectExpenseMutateRepository : JpaRepository<ProjectExpense, Long> {
    @Modifying
    @Query(
        value = "UPDATE project_expenses SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)
}

@Repository
private interface SpringProjectExpenseQueryRepository : JpaRepository<ProjectExpense, Long> {
    fun findAllByOrganizationId(organizationId: Long): List<ProjectExpense>

    fun findAllByOrganizationIdAndProjectId(
        organizationId: Long,
        projectId: Long,
        pageable: Pageable
    ): Page<ProjectExpense>

    fun findAllByOrganizationIdAndBeneficiaryId(
        organizationId: Long,
        beneficiaryId: Long
    ): List<ProjectExpense>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): ProjectExpense?

    @Query(
        value = "SELECT * FROM project_expenses WHERE organization_id = :organizationId AND deleted IS NULL AND created_at >= DATE_TRUNC('week', CURRENT_DATE - INTERVAL '1 week') AND created_at < DATE_TRUNC('week', CURRENT_DATE) ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedAtLastWeek(organizationId: Long): List<ProjectExpense>

    @Query(
        value = "SELECT * FROM project_expenses WHERE organization_id = :organizationId AND deleted IS NULL AND created_at >= DATE_TRUNC('week', CURRENT_DATE) ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedThisWeek(organizationId: Long): List<ProjectExpense>

    @Query(
        value = "SELECT COALESCE(SUM(bt.amount), 0) FROM project_expenses pe JOIN beneficiary_transactions bt ON pe.beneficiary_transaction_id = bt.id WHERE pe.organization_id = :organizationId AND pe.deleted IS NULL AND bt.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(bt.amount_paid), 0) FROM project_expenses pe JOIN beneficiary_transactions bt ON pe.beneficiary_transaction_id = bt.id WHERE pe.organization_id = :organizationId AND pe.deleted IS NULL AND bt.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(bt.amount), 0) FROM project_expenses pe JOIN beneficiary_transactions bt ON pe.beneficiary_transaction_id = bt.id WHERE pe.organization_id = :organizationId AND pe.deleted IS NULL AND bt.deleted IS NULL AND pe.created_at >= DATE_TRUNC('week', CURRENT_DATE)",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdCreatedThisWeek(organizationId: Long): BigDecimal
}

@Repository
private interface SpringProjectIncomeRepository : JpaRepository<ProjectIncome, Long> {
    fun findAllByOrganizationId(organizationId: Long): List<ProjectIncome>

    fun findAllByOrganizationIdAndProjectId(
        organizationId: Long,
        projectId: Long
    ): List<ProjectIncome>

    fun findAllByOrganizationIdAndCustomerId(
        organizationId: Long,
        customerId: Long
    ): List<ProjectIncome>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): ProjectIncome?

    fun findByCustomerTransactionIdAndOrganizationId(
        organizationId: Long,
        customerTransactionId: Long
    ): ProjectIncome?

    @Modifying
    @Query(
        value = "UPDATE project_incomes SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)

    @Query(
        value = "SELECT * FROM project_incomes WHERE organization_id = :organizationId AND deleted IS NULL AND created_at >= DATE_TRUNC('week', CURRENT_DATE - INTERVAL '1 week') AND created_at < DATE_TRUNC('week', CURRENT_DATE) ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedLastWeek(organizationId: Long): List<ProjectIncome>

    @Query(
        value = "SELECT * FROM project_incomes WHERE organization_id = :organizationId AND deleted IS NULL AND created_at >= DATE_TRUNC('week', CURRENT_DATE) ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedThisWeek(organizationId: Long): List<ProjectIncome>

    @Query(
        value = "SELECT COALESCE(SUM(ct.amount), 0) FROM project_incomes pi JOIN customer_transactions ct ON pi.customer_transaction_id = ct.id WHERE pi.organization_id = :organizationId AND pi.deleted IS NULL AND ct.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(ct.amount_paid), 0) FROM project_incomes pi JOIN customer_transactions ct ON pi.customer_transaction_id = ct.id WHERE pi.organization_id = :organizationId AND pi.deleted IS NULL AND ct.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationId(organizationId: Long): BigDecimal
}

@Repository
private interface SpringProjectAccessRepository : JpaRepository<ProjectAccess, Long> {
    fun findAllByOrganizationIdAndUserId(organizationId: Long, userId: Long): List<ProjectAccess>

    fun findAllByOrganizationIdAndProjectId(organizationId: Long, projectId: Long): List<ProjectAccess>

    fun findByUserIdAndProjectId(userId: Long, projectId: Long): ProjectAccess?

    @Modifying
    @Query(
        value = "UPDATE projects_access SET deleted = NOW(), updated_by = :updatedBy WHERE project_id = :projectId AND user_id = :userId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByProjectId(projectId: Long, userId: Long, updatedBy: Long)
}
