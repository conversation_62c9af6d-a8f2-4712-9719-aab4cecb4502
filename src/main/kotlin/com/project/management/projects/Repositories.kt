package com.project.management.projects

import com.project.management.common.entity.OrganizationId
import com.project.management.common.utility.autowired
import com.project.management.common.utility.filterDeleted
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

object ProjectRepository {
    private val repo: SpringProjectRepository by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<Project> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getById(organizationId: OrganizationId, id: Long): Project? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun sumTotalExpenses(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumTotalExpensesByOrganizationId(organizationId.value)
        }

        fun sumTotalPaidExpenses(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumTotalPaidExpensesByOrganizationId(organizationId.value)
        }

        fun sumTotalIncomes(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumTotalIncomesByOrganizationId(organizationId.value)
        }

        fun sumTotalPaidIncomes(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumTotalPaidIncomesByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(project: Project): Project {
            return repo.save(project)
        }

        fun deleteById(id: Long, organizationId: OrganizationId, updatedBy: Long) {
            repo.deleteByIdAndOrganizationId(id, organizationId.value, updatedBy)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(PROJECT_DELETED_FILTER, block)
}

object ProjectExpenseRepository {
    private val queryRepo: SpringProjectExpenseQueryRepository by autowired()
    private val mutateRepo: SpringProjectExpenseMutateRepository by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<ProjectExpense> = filterDeleted {
            queryRepo.findAllByOrganizationId(organizationId.value)
        }

        fun getAllByProjectId(
            organizationId: OrganizationId,
            projectId: Long,
            pageable: Pageable
        ): Page<ProjectExpense> = filterDeleted {
            queryRepo.findAllByOrganizationIdAndProjectId(organizationId.value, projectId, pageable)
        }

        fun getAllByBeneficiaryId(
            organizationId: OrganizationId,
            beneficiaryId: Long
        ): List<ProjectExpense> = filterDeleted {
            queryRepo.findAllByOrganizationIdAndBeneficiaryId(organizationId.value, beneficiaryId)
        }

        fun getById(organizationId: OrganizationId, id: Long): ProjectExpense? = filterDeleted {
            queryRepo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun getAllCreatedLastWeek(organizationId: OrganizationId): List<ProjectExpense> = filterDeleted {
            queryRepo.findAllByOrganizationIdCreatedAtLastWeek(organizationId.value)
        }

        fun getAllCreatedThisWeek(organizationId: OrganizationId): List<ProjectExpense> = filterDeleted {
            queryRepo.findAllByOrganizationIdCreatedThisWeek(organizationId.value)
        }

        fun sumAmount(organizationId: OrganizationId): BigDecimal = filterDeleted {
            queryRepo.sumAmountByOrganizationId(organizationId.value)
        }

        fun sumAmountPaid(organizationId: OrganizationId): BigDecimal = filterDeleted {
            queryRepo.sumAmountPaidByOrganizationId(organizationId.value)
        }

        fun sumAmountCreatedThisWeek(organizationId: OrganizationId): BigDecimal = filterDeleted {
            queryRepo.sumAmountByOrganizationIdCreatedThisWeek(organizationId.value)
        }
    }

    object Mutate {
        fun save(expense: ProjectExpenseEntity): ProjectExpenseEntity {
            return mutateRepo.save(expense)
        }

        fun deleteById(id: Long, organizationId: OrganizationId, updatedBy: Long) {
            mutateRepo.deleteByIdAndOrganizationId(id, organizationId.value, updatedBy)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(PROJECT_EXPENSE_DELETED_FILTER, block)
}

object ProjectIncomeRepository {
    private val repo: SpringProjectIncomeRepository by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<ProjectIncome> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getAllByProjectId(
            organizationId: OrganizationId,
            projectId: Long
        ): List<ProjectIncome> = filterDeleted {
            repo.findAllByOrganizationIdAndProjectId(organizationId.value, projectId)
        }

        fun getAllByCustomerId(
            organizationId: OrganizationId,
            customerId: Long
        ): List<ProjectIncome> = filterDeleted {
            repo.findAllByOrganizationIdAndCustomerId(organizationId.value, customerId)
        }

        fun getById(organizationId: OrganizationId, id: Long): ProjectIncome? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun getByCustomerTransactionId(
            organizationId: OrganizationId,
            customerTransactionId: Long
        ): ProjectIncome? = filterDeleted {
            repo.findByCustomerTransactionIdAndOrganizationId(organizationId.value, customerTransactionId)
        }

        fun sumAmount(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumAmountByOrganizationId(organizationId.value)
        }

        fun sumAmountPaid(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumAmountPaidByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(income: ProjectIncome): ProjectIncome {
            return repo.save(income)
        }

        fun deleteById(id: Long, organizationId: OrganizationId, updatedBy: Long) {
            repo.deleteByIdAndOrganizationId(id, organizationId.value, updatedBy)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(PROJECT_INCOME_DELETED_FILTER, block)
}

object ProjectAccessRepository {
    private val repo: SpringProjectAccessRepository by autowired()

    object Query {
        fun getAllByOrganizationIdAndUserId(
            organizationId: OrganizationId,
            userId: Long
        ): List<ProjectAccess> = filterDeleted {
            repo.findAllByOrganizationIdAndUserId(organizationId.value, userId)
        }

        fun getAllByOrganizationIdAndProjectId(
            organizationId: OrganizationId,
            projectId: Long
        ): List<ProjectAccess> = filterDeleted {
            repo.findAllByOrganizationIdAndProjectId(organizationId.value, projectId)
        }

        fun getByUserIdAndProjectId(userId: Long, projectId: Long): ProjectAccess? = filterDeleted {
            repo.findByUserIdAndProjectId(userId, projectId)
        }
    }

    object Mutate {
        fun save(access: ProjectAccess): ProjectAccess {
            return repo.save(access)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(PROJECT_ACCESS_DELETED_FILTER, block)
}
