package com.project.management.projects.controllers

import com.project.management.common.utility.page
import com.project.management.money.MoneyService
import com.project.management.projects.requests.PostRequestProjectAccess
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.projects.requests.PostRequestProjectIncome
import com.project.management.projects.requests.PostRequestProject
import com.project.management.projects.models.ProjectEntity
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectIncomeEntity
import com.project.management.projects.requests.PatchRequestProjectExpenseAmount
import com.project.management.projects.requests.PatchRequestProjectExpense
import com.project.management.projects.requests.PatchRequestProjectIncomeAmount
import com.project.management.projects.requests.PatchRequestProject
import com.project.management.projects.services.ProjectExpensesService
import com.project.management.projects.services.ProjectIncomesService
import com.project.management.projects.services.ProjectService
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/projects")
class ProjectController(
    private val projectService: ProjectService,
    private val query: ProjectExpensesService,
    private val mutate: ProjectExpensesMutateService,
    private val money: MoneyService,
    private val projectIncomesService: ProjectIncomesService
) {
    @GetMapping
    fun getAll(): ResponseEntity<List<ProjectEntity>> {
        return ResponseEntity.ok(projectService.getAll())
    }

    @PostMapping
    fun create(@RequestBody projectRequestDto: PostRequestProject): ResponseEntity<ProjectEntity> {
        return ResponseEntity.ok(projectService.create(projectRequestDto))
    }

    @GetMapping("/{projectId}")
    fun getById(@PathVariable projectId: Long): ResponseEntity<ProjectEntity> {
        return ResponseEntity.ok(projectService.getById(projectId))
    }

    @GetMapping("/{projectId}/expenses")
    fun getExpenses(
        @PathVariable projectId: Long,
        @RequestParam(required = false) limit: Int?,
        @RequestParam(required = false) offset: Int?,
        @RequestParam(required = false) sort: String?,
        @RequestParam(required = false) by: String?
    ): ResponseEntity<List<ProjectExpense>> {
        val page = page(limit, offset, sort, by)
        return ResponseEntity.ok(query.getAllByProjectId(projectId, page))
    }

    @PostMapping("/{projectId}/expenses")
    fun createExpense(
        @PathVariable projectId: Long,
        @RequestBody projectExpenseRequestDto: PostRequestProjectExpense
    ): ResponseEntity<ProjectExpense> {
        return ResponseEntity.ok(money.projectExpenseAdd(projectExpenseRequestDto, projectId))
    }

    @GetMapping("/expenses/beneficiaries/{beneficiaryId}")
    fun getExpensesByBeneficiaryId(
        @PathVariable beneficiaryId: Long
    ): ResponseEntity<List<ProjectExpense>> {
        return ResponseEntity.ok(query.getAllByBeneficiaryId(beneficiaryId))
    }

    @GetMapping("/{projectId}/incomes")
    fun getIncomes(@PathVariable projectId: Long): ResponseEntity<List<ProjectIncomeEntity>> {
        return ResponseEntity.ok(projectIncomesService.getAllByProjectId(projectId))
    }

    @GetMapping("/incomes/customers/{customerId}")
    fun getIncomesByCustomerId(
        @PathVariable customerId: Long
    ): ResponseEntity<List<ProjectIncomeEntity>> {
        return ResponseEntity.ok(projectIncomesService.getAllIncomesByCustomerId(customerId))
    }

    @PostMapping("/{projectId}/incomes")
    fun createIncome(
        @PathVariable projectId: Long,
        @RequestBody projectIncomeRequestDto: PostRequestProjectIncome
    ): ResponseEntity<ProjectIncomeEntity> {
        return ResponseEntity.ok(money.projectIncomeAdd(projectIncomeRequestDto, projectId))
    }

    @PostMapping("/{projectId}/access")
    fun addAccess(
        @PathVariable projectId: Long,
        @RequestBody body: PostRequestProjectAccess
    ): ResponseEntity<Unit> {
        projectService.addAccess(body, projectId)
        return ResponseEntity.status(HttpStatus.CREATED).build()
    }

    @PatchMapping("/{projectId}/access")
    fun modifyAccess(
        @PathVariable projectId: Long,
        @RequestBody body: PostRequestProjectAccess
    ): ResponseEntity<Unit> {
        projectService.modifyAccess(body, projectId)
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }

    @DeleteMapping("/{projectId}/access")
    fun removeAccess(
        @PathVariable projectId: Long,
        @RequestBody body: Map<String, Any>
    ): ResponseEntity<Unit> {
        projectService.removeAccess(projectId, (body["userId"] as Integer).toLong())
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }

    @PatchMapping("/{projectId}")
    fun editProject(
        @PathVariable projectId: Long,
        @RequestBody body: PatchRequestProject
    ): ResponseEntity<ProjectEntity> {
        return ResponseEntity.ok(projectService.editProject(body, projectId))
    }

    @PostMapping("/{projectId}/incomes/{projectIncomeId}/amount")
    fun modifyAmount(
        @PathVariable projectIncomeId: Long,
        @RequestBody request: PatchRequestProjectIncomeAmount
    ): ResponseEntity<ProjectIncomeEntity> {
        return ResponseEntity.ok(money.projectIncomeUpdate(request, projectIncomeId))
    }

    @DeleteMapping("/{projectId}/incomes/{projectIncomeId}")
    fun deleteIncome(
        @PathVariable projectIncomeId: Long,
        @PathVariable projectId: Long
    ): ResponseEntity<Unit> {
        money.projectIncomeDelete(projectIncomeId)
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }

    @PostMapping("/{projectId}/expenses/{projectExpenseId}/amount")
    fun modifyAmount(
        @PathVariable projectExpenseId: Long,
        @RequestBody request: PatchRequestProjectExpenseAmount
    ): ResponseEntity<ProjectExpense> {
        return ResponseEntity.ok(money.projectExpenseUpdate(request, projectExpenseId))
    }

    @DeleteMapping("/{projectId}/expenses/{projectExpenseId}")
    fun deleteExpense(
        @PathVariable projectExpenseId: Long,
        @PathVariable projectId: Long
    ): ResponseEntity<Unit> {
        money.projectExpenseDelete(projectExpenseId)
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }

    @PatchMapping("/{projectId}/expenses/{projectExpenseId}")
    fun modifyExpenseOld(
        @PathVariable projectExpenseId: Long,
        @RequestBody request: PatchRequestProjectExpense
    ): ResponseEntity<ProjectExpense> {
        return ResponseEntity.ok(mutate.updateProjectExpense(request, projectExpenseId))
    }

    @PatchMapping("/expenses/{projectExpenseId}")
    fun modifyExpense(
        @PathVariable projectExpenseId: Long,
        @RequestBody request: PatchRequestProjectExpense
    ): ResponseEntity<ProjectExpense> {
        return ResponseEntity.ok(mutate.update(request, projectExpenseId))
    }

    @DeleteMapping("/{projectId}")
    fun deleteProject(
        @PathVariable projectId: Long
    ): ResponseEntity<Unit> {
        projectService.delete(projectId)
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }
}