package com.project.management.projects.requests

import com.project.management.beneficiaries.BeneficiaryTransaction
import com.project.management.beneficiaries.BeneficiaryTransactionRepository
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.users.User

data class PatchRequestProjectExpenseAmount(
    val amount: Double,
    val amountPaid: Double,
    val transactionVersion: Long
) {

    fun validate(beneficiaryTransactionId: Long, user: User): BeneficiaryTransaction {
        val transaction = BeneficiaryTransactionRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = beneficiaryTransactionId
        ).validate()
        if (!user.isAdmin) {
                throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        if (transaction.version != transactionVersion) throw BusinessException.ConflictException()

        if (amountPaid > amount) {
            throw BusinessException.BadRequestException(message = "Amount paid cannot be greater than amount.")
        }

        return transaction
    }
}

data class PatchRequestProjectIncomeAmount(
    val amount: Double,
    val amountPaid: Double,
    val transactionVersion: Long
)

data class PatchRequestProject(
    val name: String?,
    val description: String?,
    val customerId: Long?,
    val version: Long,
)

data class PostRequestProjectAccess(
    val expensesAccess: Boolean,
    val incomesAccess: Boolean,
    val userId: Long,
    val version: Long = 1,
)

data class PostRequestProjectExpense(
    val amount: Double,
    val amountPaid: Double = amount, // TODO  remove this amount and take it from request body
    val description: String,
    val transactionDate: String,
    val beneficiaryId: Long?,
    val termsGroupId: Long?,
    val termId: Long?
)

data class PatchRequestProjectExpense(
    val amount: Double,
    val amountPaid: Double = amount, // TODO  remove this amount and take it from request body
    val description: String,
    val transactionDate: String,
    val projectId: Long,
    val beneficiaryId: Long?,
    val termsGroupId: Long?,
    val termId: Long?,
    val version: Long,
    val transactionVersion: Long
) {
    fun toAmountRequest(): PatchRequestProjectExpenseAmount {
        return PatchRequestProjectExpenseAmount(
            amount = amount,
            amountPaid = amountPaid,
            transactionVersion = transactionVersion
        )
    }
}

data class PostRequestProjectIncome(
    val amount: Double,
    val amountPaid: Double,
    val description: String,
    val transactionDate: String,
    val customerId: Long
)

data class PostRequestProject(
    val name: String,
    val description: String,
    val isActive: Boolean,
    val customerId: Long,
)