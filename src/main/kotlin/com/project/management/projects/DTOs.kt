package com.project.management.projects

import com.project.management.beneficiaries.BeneficiaryTransaction
import com.project.management.beneficiaries.BeneficiaryTransactionRepository
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.users.User

data class ProjectPostRequest(
    val name: String,
    val description: String,
    val isActive: Boolean,
    val customerId: Long,
)

data class ProjectPatchRequest(
    val name: String?,
    val description: String?,
    val customerId: Long?,
    val version: Long,
)

data class ProjectAccessPostRequest(
    val expensesAccess: <PERSON><PERSON><PERSON>,
    val incomesAccess: <PERSON>olean,
    val userId: Long,
    val version: Long = 1,
)

data class ProjectExpensePostRequest(
    val amount: Double,
    val amountPaid: Double = amount,
    val description: String,
    val transactionDate: String,
    val beneficiaryId: Long?,
    val termsGroupId: Long?,
    val termId: Long?
) {
    fun validate(user: User, projectId: Long) {
        // Validate project exists
        ProjectRepository.Query.getById(organizationId = user, id = projectId).validate()

        // Validate amount constraints
        if (amountPaid > amount) {
            throw BusinessException.BadRequestException(message = "Amount paid cannot be greater than amount.")
        }
    }
}

data class ProjectExpensePatchRequest(
    val amount: Double,
    val amountPaid: Double = amount,
    val description: String,
    val transactionDate: String,
    val projectId: Long,
    val beneficiaryId: Long?,
    val termsGroupId: Long?,
    val termId: Long?,
    val version: Long,
    val transactionVersion: Long
) {
    fun validate(user: User): BeneficiaryTransaction {
        val transaction = BeneficiaryTransactionRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = transactionVersion
        ).validate()

        if (!user.isAdmin) {
            throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        if (transaction.version != transactionVersion) throw BusinessException.ConflictException()

        if (amountPaid > amount) {
            throw BusinessException.BadRequestException(message = "Amount paid cannot be greater than amount.")
        }

        return transaction
    }
}

data class ProjectIncomePostRequest(
    val amount: Double,
    val amountPaid: Double,
    val description: String,
    val transactionDate: String,
    val customerId: Long
) {
    fun validate(user: User, projectId: Long) {
        // Validate project exists
       ProjectRepository.Query.getById(organizationId = user, id = projectId).validate()

        // Validate amount constraints
        if (amountPaid > amount) {
            throw BusinessException.BadRequestException(message = "Amount paid cannot be greater than amount.")
        }
    }
}

data class ProjectIncomeAmountPatchRequest(
    val amount: Double,
    val amountPaid: Double,
    val transactionVersion: Long
)

data class ProjectExpenseAmountPatchRequest(
    val amount: Double,
    val amountPaid: Double,
    val transactionVersion: Long
) {
    fun validate(beneficiaryTransactionId: Long, user: User): BeneficiaryTransaction {
        val transaction = BeneficiaryTransactionRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = beneficiaryTransactionId
        ).validate()

        if (!user.isAdmin) {
            throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        if (transaction.version != transactionVersion) throw BusinessException.ConflictException()

        if (amountPaid > amount) {
            throw BusinessException.BadRequestException(message = "Amount paid cannot be greater than amount.")
        }

        return transaction
    }
}

data class PostRequestProjectIncomePay(
    val amount: Double,
    val amountPaid: Double,
    val transactionVersion: Long
) {
    fun toModifyAmount(originalAmount: Double): ProjectIncomeAmountPatchRequest {
        return ProjectIncomeAmountPatchRequest(
            amount = originalAmount,
            amountPaid = amountPaid,
            transactionVersion = transactionVersion
        )
    }
}