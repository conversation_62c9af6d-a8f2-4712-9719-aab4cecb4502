package com.project.management.common.entity

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import com.project.management.users.User
import jakarta.persistence.Column
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.MappedSuperclass
import jakarta.persistence.OneToOne
import jakarta.persistence.PreUpdate
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.Transient
import java.time.ZonedDateTime

@MappedSuperclass
abstract class OrganizationId {
    @get:JsonProperty("organizationId")
    abstract val organizationId: Long

    @get:JsonIgnore
    val value: Long get() = organizationId
}

fun OrganizationId(id: Long): OrganizationId {
    return object : OrganizationId() {
        override val organizationId: Long = id
    }
}

@MappedSuperclass
abstract class Id : OrganizationId() {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private var _id: Long? = null

    val id: Long get() = _id!!
}

@MappedSuperclass
abstract class AuditableTime : com.project.management.common.entity.Id() {

    @CreationTimestamp
    lateinit var createdAt: ZonedDateTime
        protected set

    @UpdateTimestamp
    lateinit var updatedAt: ZonedDateTime
        protected set

}

@MappedSuperclass
abstract class AuditableVersion : AuditableTime() {
    @Column(name = "version")
    private var _version: Long = 1

    val version: Long get() = _version

    @PreUpdate
    fun setLastUpdate() {
        _version += 1
    }
}

@MappedSuperclass
abstract class AuditableDeletable : AuditableVersion() {
    @Column(name = "deleted")
    private var _deleted: ZonedDateTime? = null

    @get:Column(name = "deleted")
    val deleted: ZonedDateTime? get() = _deleted
}

@MappedSuperclass
abstract class AuditableNullableUser : AuditableDeletable() {
    @get:Column(updatable = false)
    @get:JsonProperty("createdBy")
    abstract val createdBy: Long?

    @get:JsonProperty("updatedBy")
    abstract var updatedBy: Long?

    @get:JsonProperty("createdById")
    open val createdById: Long? get() = createdBy

    @get:JsonProperty("updatedById")
    open val updatedById: Long? get() = updatedBy
}

@MappedSuperclass
abstract class AuditableUser : AuditableDeletable() {
    @get:Column(updatable = false)
    @get:JsonProperty("createdBy")
    abstract val createdBy: Long

    @get:JsonProperty("updatedBy")
    abstract var updatedBy: Long

    @get:JsonProperty("createdById")
    open val createdById: Long get() = createdBy

    @get:JsonProperty("updatedById")
    open val updatedById: Long get() = updatedBy

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "createdBy", updatable = false, insertable = false)
    open lateinit var createdByUser: User
}

abstract class ServerEntity : AuditableUser()

abstract class EntityNullableUser : AuditableNullableUser()