package com.project.management.money

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.customers.PostRequestProjectIncomePay
import com.project.management.money.organization.OrganizationCapitalMoneyMutateService
import com.project.management.money.project.ProjectMoneyMutateService
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.organization.CapitalTransaction
import com.project.management.organization.CapitalTransactionRequest
import com.project.management.projects.ProjectExpense
import com.project.management.projects.ProjectIncome
import com.project.management.projects.ProjectExpenseAmountPatchRequest
import com.project.management.projects.ProjectIncomeAmountPatchRequest
import com.project.management.projects.ProjectExpensePostRequest
import com.project.management.projects.ProjectIncomePostRequest
import com.project.management.users.User
import jakarta.transaction.Transactional
import org.springframework.stereotype.Component

@Component
class MoneyService(
    private val performer: CurrentUserConfig,
    private val user: UserMoneyMutateService,
    private val capital: OrganizationCapitalMoneyMutateService,
    private val project: ProjectMoneyMutateService,
    private val money: MoneyValidationService,
) {

    @Transactional
    fun userAddBalance(userId: Long, amount: Double): User {
        val response = user.userBalanceAdd(userId, amount)
        money.validate(response.organizationId)
        return response
    }

    @Transactional
    fun userDecreaseBalance(userId: Long, amount: Double): User {
        val response = user.userBalanceDecrease(userId, amount)
        money.validate(response.organizationId)
        return response
    }

    @Transactional
    fun projectExpenseAdd(request: ProjectExpensePostRequest, projectId: Long): ProjectExpense {
        val response = project.projectExpenseAdd(request, projectId)
        money.validate(response.project.organizationId)
        return response
    }

    @Transactional
    fun projectExpenseUpdate(request: ProjectExpenseAmountPatchRequest, projectExpenseId: Long): ProjectExpense {
        val response = project.projectExpenseUpdate(request, projectExpenseId)
        money.validate(response.project.organizationId)
        return response
    }

    @Transactional
    fun projectExpenseDelete(projectExpenseId: Long) {
        project.projectExpenseDelete(projectExpenseId)
        money.validate(performer.getCurrentUser().organizationId)
    }

    @Transactional
    fun projectExpenseChangeProject(expenseId: Long, oldProjectId: Long, newProjectId: Long) {
        project.projectExpenseChangeProject(expenseId, oldProjectId, newProjectId)
        money.validate(performer.getCurrentUser().organizationId)
    }

    @Transactional
    fun projectExpenseChangeBeneficiary(
        expenseId: Long,
        oldBeneficiaryId: Long?,
        newBeneficiaryId: Long?
    ) {
        project.projectExpenseChangeBeneficiary(expenseId, oldBeneficiaryId, newBeneficiaryId)
        money.validate(performer.getCurrentUser().organizationId)
    }

    @Transactional
    fun projectIncomeAdd(request: ProjectIncomePostRequest, projectId: Long): ProjectIncome {
        val response = project.projectIncomeAdd(request, projectId)
        money.validate(response.project.organizationId)
        return response
    }

    @Transactional
    fun projectIncomeDelete(projectIncomeId: Long) {
        project.projectIncomeDelete(projectIncomeId)
        money.validate(performer.getCurrentUser().organizationId)
    }

    @Transactional
    fun projectIncomeUpdate(request: ProjectIncomeAmountPatchRequest, projectIncomeId: Long): ProjectIncome {
        val response = project.projectIncomeUpdate(request, projectIncomeId)
        money.validate(response.project.organizationId)
        return response
    }

    @Transactional
    fun projectIncomePay(request: PostRequestProjectIncomePay, customerTransactionId: Long): ProjectIncome {
        val response = project.projectIncomePay(request, customerTransactionId)
        money.validate(response.project.organizationId)
        return response
    }

    @Transactional
    fun addCapital(request: CapitalTransactionRequest): CapitalTransaction {
        val response = capital.addCapital(request)
        money.validate(response.organizationId)
        return response
    }

    @Transactional
    fun removeCapital(request: CapitalTransactionRequest): CapitalTransaction {
        val response = capital.removeCapital(request)
        money.validate(response.organizationId)
        return response
    }
}