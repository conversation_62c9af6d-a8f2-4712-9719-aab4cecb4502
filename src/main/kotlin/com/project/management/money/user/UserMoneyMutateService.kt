package com.project.management.money.user

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.validate
import com.project.management.money.MoneyValidationService
import com.project.management.money.organization.OrganizationBalanceMutateService
import com.project.management.projects.Project
import com.project.management.projects.ProjectExpense
import com.project.management.projects.ProjectExpenseAmountPatchRequest
import com.project.management.users.BalanceTransaction
import com.project.management.users.BalanceTransactionRepository
import com.project.management.users.User
import com.project.management.users.UserTransactionTag
import com.project.management.users.UserRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class UserMoneyMutateService(
    private val organizationBalance: OrganizationBalanceMutateService,
    private val money: MoneyValidationService,
    private val currentUser: CurrentUserConfig
) {

    @Transactional
    fun projectExpenseAdd(userId: Long, expense: ProjectExpense): User {
        return decreaseBalance(
            userId = userId,
            amount = expense.beneficiaryTransaction.amountPaid.toDouble(),
            description = expense.project.description(expense.beneficiaryTransaction.description),
            tag = UserTransactionTag.PROJECT_EXPENSE_ADD,
            reference = expense.project.id.toString()
        )
    }

    @Transactional
    fun projectExpenseDelete(
        userId: Long, amount: Double, description: String, project: Project
    ): User {
        return increaseBalance(
            userId = userId,
            amount = amount,
            description = project.description(description),
            tag = UserTransactionTag.PROJECT_EXPENSE_DELETE,
            reference = project.id.toString()
        )
    }

    @Transactional
    fun projectExpenseModify(
        expense: ProjectExpense, request: ProjectExpenseAmountPatchRequest
    ): User {
        val amount = expense.beneficiaryTransaction.amountPaid.negate() + request.amountPaid.toBigDecimal()
        val description = expense.beneficiaryTransaction.description.trim() +
                "\n" + "${expense.beneficiaryTransaction.amountPaid}  >  $${request.amountPaid}"
        return increaseBalance(
            userId = expense.createdBy!!,
            amount = amount.negate().toDouble(),
            description = expense.project.description(description),
            tag = UserTransactionTag.PROJECT_EXPENSE_MODIFY,
            reference = expense.project.id.toString()
        )
    }

    @Transactional
    fun userBalanceAdd(userId: Long, amount: Double): User {
        val user = increaseBalance(
            userId = userId,
            amount = amount,
            description = "",
            tag = UserTransactionTag.BALANCE_ADD,
            reference = null
        )
        organizationBalance.decreaseAvailableBalance(amount.toBigDecimal())
        money.validate(user.organizationId)
        return user
    }

    @Transactional
    fun userBalanceDecrease(userId: Long, amount: Double): User {
        val user = decreaseBalance(
            userId = userId,
            amount = amount,
            description = "",
            tag = UserTransactionTag.BALANCE_WITHDRAW,
            reference = null
        )
        organizationBalance.increaseAvailableBalance(amount.toBigDecimal())
        money.validate(user.organizationId)
        return user
    }


    private fun increaseBalance(
        userId: Long,
        amount: Double,
        description: String,
        tag: UserTransactionTag,
        reference: String?
    ): User {
        val currentUser = currentUser.getCurrentUser()
        var user = UserRepository.Query.getById(currentUser, userId).validate()
        transaction(
            user = user,
            amount = amount.toBigDecimal(),
            description = description,
            tag = tag,
            reference = reference
        )

        user.balance = user.balance.plus(amount.toBigDecimal())
        user.updatedBy = currentUser.id
        user = UserRepository.Mutate.save(user)

        return user
    }

    private fun decreaseBalance(
        userId: Long,
        amount: Double,
        description: String,
        tag: UserTransactionTag,
        reference: String?
    ): User {
        val currentUser = currentUser.getCurrentUser()
        var user = UserRepository.Query.getById(currentUser, userId).validate()

        transaction(
            user = user,
            amount = amount.toBigDecimal().negate(),
            description = description,
            tag = tag,
            reference = reference
        )

        user.balance = user.balance.minus(amount.toBigDecimal())
        user.updatedBy = currentUser.id
        user = UserRepository.Mutate.save(user)

        return user
    }

    private fun transaction(
        user: User,
        description: String,
        amount: BigDecimal,
        tag: UserTransactionTag,
        reference: String?
    ): BalanceTransaction {
        val currentAmount = user.balance + amount
        val loggedInUser = currentUser.getCurrentUser()

        val transaction = BalanceTransaction(
            organizationId = loggedInUser.organizationId,
            createdBy = loggedInUser.id,
            updatedBy = loggedInUser.id,
            userId = user.id,
            amount = amount,
            currentAmount = currentAmount,
            description = description,
            transactionTag = tag,
            reference = reference,
            createdByUser = user
        )
        return BalanceTransactionRepository.Mutate.save(transaction)
    }

    private fun Project.description(description: String): String {
        return "${description.trim()}\n${this.name.trim()}".trim()
    }
}