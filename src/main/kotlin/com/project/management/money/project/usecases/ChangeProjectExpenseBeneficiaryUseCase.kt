package com.project.management.money.project.usecases

import com.project.management.beneficiaries.BeneficiaryRepository
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.beneficiary.BeneficiaryMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.ProjectExpense
import com.project.management.projects.ProjectExpenseRepository
import com.project.management.projects.ProjectRepository
import org.springframework.stereotype.Component

@Component
class ChangeProjectExpenseBeneficiaryUseCase(
    private val currentUser: CurrentUserConfig,
    private val mutate: ProjectExpensesIncomesMoneyMutateService,
    private val beneficiary: BeneficiaryMoneyMutateService
) {

    fun changeBeneficiary(expenseId: Long, oldBeneficiaryId: Long?, newBeneficiaryId: Long?) {
        val currentUser = currentUser.getCurrentUser()
        val expense = ProjectExpenseRepository.Query.getById(
            organizationId = OrganizationId(currentUser.organizationId),
            id = expenseId
        ).validate()
        if (expense.beneficiaryId == newBeneficiaryId) return

        validation(expense, oldBeneficiaryId, newBeneficiaryId)

        projectScenarios(expense, newBeneficiaryId)

        beneficiaryScenarios(expense, oldBeneficiaryId, newBeneficiaryId)
    }

    fun projectScenarios(expense: ProjectExpense, newBeneficiaryId: Long?) {
        mutate.projectExpenseChangeBeneficiary(
            expense = expense,
            newBeneficiaryId = newBeneficiaryId
        )
    }

    private fun beneficiaryScenarios(
        expense: ProjectExpense,
        oldBeneficiaryId: Long?,
        newBeneficiaryId: Long?
    ) {
        beneficiary.projectExpenseChangeBeneficiary(
            transactionId = expense.beneficiaryTransaction.id!!,
            oldBeneficiaryId = oldBeneficiaryId,
            newBeneficiaryId = newBeneficiaryId
        )
    }

    private fun validation(
        expense: ProjectExpense,
        oldBeneficiaryId: Long?,
        newBeneficiaryId: Long?
    ) {
        val currentUser = currentUser.getCurrentUser()

        if (expense.beneficiaryId != oldBeneficiaryId) {
            throw BusinessException.BadRequestException(message = "Expense is not in the old beneficiary.")
        }
        if (expense.beneficiaryId == newBeneficiaryId) {
            throw BusinessException.BadRequestException(message = "Expense is already in the new beneficiary.")
        }

        if (oldBeneficiaryId != null)
            BeneficiaryRepository.Query
                .getById(OrganizationId(currentUser.organizationId), oldBeneficiaryId).validate()
        if (newBeneficiaryId != null)
            BeneficiaryRepository.Query
                .getById(OrganizationId(currentUser.organizationId), newBeneficiaryId).validate()
    }
}