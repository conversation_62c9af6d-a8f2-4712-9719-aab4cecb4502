package com.project.management.money.project.usecases

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.beneficiary.BeneficiaryMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.money.project.ProjectMoneyMutateService
import com.project.management.projects.ProjectExpense
import com.project.management.projects.ProjectExpenseRepository
import com.project.management.projects.ProjectRepository
import org.springframework.stereotype.Component

@Component
class ChangeProjectExpenseUseCase(
    private val currentUser: CurrentUserConfig,
    private val mutate: ProjectExpensesIncomesMoneyMutateService,
    private val beneficiary: BeneficiaryMoneyMutateService
) {

    fun changeProject(expenseId: Long, oldProjectId: Long, newProjectId: Long) {
        val currentUser = currentUser.getCurrentUser()
        val expense = ProjectExpenseRepository.Query.getById(
            organizationId = OrganizationId(currentUser.organizationId),
            id = expenseId
        ).validate()
        if (expense.projectId == newProjectId) return

        validate(expense, oldProjectId, newProjectId)

        projectScenarios(expense, oldProjectId, newProjectId)

        beneficiaryScenarios(expense, newProjectId)
    }

    fun projectScenarios(expense: ProjectExpense, oldProjectId: Long, newProjectId: Long) {
        mutate.projectExpenseChange(
            expense = expense,
            oldProjectId = oldProjectId,
            newProjectId = newProjectId
        )
    }

    private fun beneficiaryScenarios(expense: ProjectExpense, newProjectId: Long) {
        beneficiary.projectExpenseChangeProject(
            transaction = expense.beneficiaryTransaction, newProjectId = newProjectId
        )
    }

    private fun validate(expense: ProjectExpense, oldProjectId: Long, newProjectId: Long) {
        val currentUser = currentUser.getCurrentUser()

        if (expense.projectId != oldProjectId) {
            throw BusinessException.BadRequestException(message = "Expense is not in the old project.")
        }
        if (expense.projectId == newProjectId) {
            throw BusinessException.BadRequestException(message = "Expense is already in the new project.")
        }

        ProjectRepository.Query.getById(
            organizationId = OrganizationId(currentUser.organizationId),
            id = oldProjectId
        ).validate()

        ProjectRepository.Query.getById(
            organizationId = OrganizationId(currentUser.organizationId),
            id = newProjectId
        ).validate()
    }
}