package com.project.management.money.project.usecases

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.beneficiary.BeneficiaryMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.money.project.ProjectMoneyMutateService
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectValidator
import org.springframework.stereotype.Component

@Component
class ChangeProjectExpenseUseCase(
    private val currentUser: CurrentUserConfig,
    private val validation: ProjectExpenseValidator,
    private val projects: ProjectValidator,
    private val mutate: ProjectExpensesIncomesMoneyMutateService,
    private val beneficiary: BeneficiaryMoneyMutateService
) {

    fun changeProject(expenseId: Long, oldProjectId: Long, newProjectId: Long) {
        val currentUser = currentUser.getCurrentUser()
        val expense = validation.validateExistsByIdAndOrganization(
            projectExpenseId = expenseId,
            organizationId = currentUser.organizationId
        )
        if (expense.projectId == newProjectId) return

        validation(expense, oldProjectId, newProjectId)

        projectScenarios(expense, oldProjectId, newProjectId)

        beneficiaryScenarios(expense, newProjectId)
    }

    fun projectScenarios(expense: ProjectExpense, oldProjectId: Long, newProjectId: Long) {
        mutate.projectExpenseChange(
            expense = expense,
            oldProjectId = oldProjectId,
            newProjectId = newProjectId
        )
    }

    private fun beneficiaryScenarios(expense: ProjectExpense, newProjectId: Long) {
        beneficiary.projectExpenseChangeProject(
            transaction = expense.beneficiaryTransaction, newProjectId = newProjectId
        )
    }

    private fun validation(expense: ProjectExpense, oldProjectId: Long, newProjectId: Long) {
        val currentUser = currentUser.getCurrentUser()

        if (expense.projectId != oldProjectId) {
            throw BusinessException.BadRequestException(message = "Expense is not in the old project.")
        }
        if (expense.projectId == newProjectId) {
            throw BusinessException.BadRequestException(message = "Expense is already in the new project.")
        }

        projects.validateExistsByIdAndOrganizationId(
            projectId = oldProjectId,
            organizationId = currentUser.organizationId
        )
        projects.validateExistsByIdAndOrganizationId(
            projectId = newProjectId,
            organizationId = currentUser.organizationId
        )
    }
}