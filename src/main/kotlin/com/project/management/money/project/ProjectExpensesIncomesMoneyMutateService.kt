package com.project.management.money.project

import com.project.management.beneficiaries.BeneficiaryTransaction
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.customers.CustomerTransaction
import com.project.management.customers.CustomerRepository
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.projects.Project
import com.project.management.projects.ProjectExpense
import com.project.management.projects.ProjectIncome
import com.project.management.projects.ProjectExpenseRepository
import com.project.management.projects.ProjectIncomeRepository
import com.project.management.projects.ProjectRepository
import com.project.management.projects.ProjectExpensePostRequest
import com.project.management.projects.ProjectIncomePostRequest
import com.project.management.projects.toModel
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class ProjectExpensesIncomesMoneyMutateService(
    private val currentUser: CurrentUserConfig,
) {

    @Transactional
    fun projectExpenseAdd(
        request: ProjectExpensePostRequest,
        transaction: BeneficiaryTransaction,
        projectId: Long
    ): ProjectExpense {
        increaseExpenses(
            projectId = projectId,
            amount = request.amount.toBigDecimal(),
            amountPaid = request.amountPaid.toBigDecimal()
        )

        return createExpense(request, transaction, projectId)
    }

    @Transactional
    fun projectExpenseDelete(expense: ProjectExpense): Project {
        deleteExpense(projectExpenseId = expense.id!!)
        return decreaseExpenses(
            projectId = expense.projectId,
            amount = expense.beneficiaryTransaction.amount,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
    }

    @Transactional
    fun projectExpenseModify(
        projectId: Long,
        oldAmount: BigDecimal,
        oldAmountPaid: BigDecimal,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): Project {
        val amount = oldAmount.negate() + newAmount
        val amountPaid = oldAmountPaid.negate() + newAmountPaid

        return increaseExpenses(
            projectId = projectId,
            amount = amount,
            amountPaid = amountPaid,
        )
    }

    @Transactional
    fun projectExpenseChange(expense: ProjectExpense, oldProjectId: Long, newProjectId: Long) {
        decreaseExpenses(
            projectId = oldProjectId,
            amount = expense.beneficiaryTransaction.amount,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
        increaseExpenses(
            projectId = newProjectId,
            amount = expense.beneficiaryTransaction.amount,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
        expense.projectId = newProjectId
        ProjectExpenseRepository.Mutate.save(expense)
    }

    @Transactional
    fun projectExpenseChangeBeneficiary(
        expense: ProjectExpense,
        newBeneficiaryId: Long?
    ) {
        expense.beneficiaryId = newBeneficiaryId
        ProjectExpenseRepository.Mutate.save(expense)
    }

    @Transactional
    fun projectIncomeAdd(
        request: ProjectIncomePostRequest,
        transaction: CustomerTransaction,
        projectId: Long
    ): ProjectIncome {
        increaseIncomes(
            projectId = projectId,
            amount = request.amount.toBigDecimal(),
            amountPaid = request.amountPaid.toBigDecimal()
        )

        return createIncome(request, transaction, projectId)
    }

    @Transactional
    fun projectIncomeDelete(income: ProjectIncome): Project {
        deleteIncome(projectIncomeId = income.id!!)
        return decreaseIncomes(
            projectId = income.projectId,
            amount = income.customerTransaction.amount,
            amountPaid = income.customerTransaction.amountPaid
        )
    }

    @Transactional
    fun projectIncomeModify(
        projectId: Long,
        oldAmount: BigDecimal,
        oldAmountPaid: BigDecimal,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): Project {
        val amount = oldAmount.negate() + newAmount
        val amountPaid = oldAmountPaid.negate() + newAmountPaid

        return increaseIncomes(
            projectId = projectId,
            amount = amount,
            amountPaid = amountPaid,
        )
    }

    private fun increaseExpenses(
        projectId: Long, amount: BigDecimal, amountPaid: BigDecimal
    ): Project {
        val currentUser = currentUser.getCurrentUser()
        val project = ProjectRepository.Query.getById(
            organizationId = OrganizationId(currentUser.organizationId),
            id = projectId
        ).validate()

        project.totalExpenses = project.totalExpenses.plus(amount)
        project.totalPaidExpenses = project.totalPaidExpenses.plus(amountPaid)
        project.updatedBy = currentUser.id!!

        return ProjectRepository.Mutate.save(project)
    }

    private fun decreaseExpenses(
        projectId: Long, amount: BigDecimal, amountPaid: BigDecimal
    ): Project {
        val currentUser = currentUser.getCurrentUser()
        val project = ProjectRepository.Query.getById(
            organizationId = OrganizationId(currentUser.organizationId),
            id = projectId
        ).validate()

        project.totalExpenses = project.totalExpenses.minus(amount)
        project.totalPaidExpenses = project.totalPaidExpenses.minus(amountPaid)
        project.updatedBy = currentUser.id!!

        return ProjectRepository.Mutate.save(project)
    }

    private fun createExpense(
        request: ProjectExpensePostRequest,
        transaction: BeneficiaryTransaction,
        projectId: Long
    ): ProjectExpense {
        val currentUser = currentUser.getCurrentUser()
        val expense = request.toModel(
            projectId = projectId,
            beneficiaryTransaction = transaction,
            user = currentUser
        )

        return ProjectExpenseRepository.Mutate.save(expense)
    }

    private fun deleteExpense(
        projectExpenseId: Long
    ) {
        val user = currentUser.getCurrentUser()
        ProjectExpenseRepository.Mutate.deleteById(
            id = projectExpenseId,
            organizationId = OrganizationId(user.organizationId),
            updatedBy = user.id!!
        )
    }

    private fun increaseIncomes(
        projectId: Long, amount: BigDecimal, amountPaid: BigDecimal
    ): Project {
        val currentUser = currentUser.getCurrentUser()
        val project = ProjectRepository.Query.getById(
            organizationId = OrganizationId(currentUser.organizationId),
            id = projectId
        ).validate()

        project.totalIncomes = project.totalIncomes.plus(amount)
        project.totalPaidIncomes = project.totalPaidIncomes.plus(amountPaid)
        project.updatedBy = currentUser.id!!

        return ProjectRepository.Mutate.save(project)
    }

    private fun decreaseIncomes(
        projectId: Long, amount: BigDecimal, amountPaid: BigDecimal
    ): Project {
        val currentUser = currentUser.getCurrentUser()
        val project = ProjectRepository.Query.getById(
            organizationId = OrganizationId(currentUser.organizationId),
            id = projectId
        ).validate()

        project.totalIncomes = project.totalIncomes.minus(amount)
        project.totalPaidIncomes = project.totalPaidIncomes.minus(amountPaid)
        project.updatedBy = currentUser.id!!

        return ProjectRepository.Mutate.save(project)
    }

    private fun createIncome(
        request: ProjectIncomePostRequest,
        transaction: CustomerTransaction,
        projectId: Long
    ): ProjectIncome {
        val currentUser = currentUser.getCurrentUser()
        val customer = CustomerRepository.Query.getById(currentUser, request.customerId).validate()
        val project = ProjectRepository.Query.getById(
            organizationId = OrganizationId(currentUser.organizationId),
            id = projectId
        ).validate()

        val income = request.toModel(
            projectId = projectId,
            customerTransaction = transaction,
            customer = customer,
            project = project,
            user = currentUser
        )

        return ProjectIncomeRepository.Mutate.save(income)
    }

    private fun deleteIncome(
        projectIncomeId: Long
    ) {
        val user = currentUser.getCurrentUser()
        ProjectIncomeRepository.Mutate.deleteById(
            id = projectIncomeId,
            organizationId = user,
            updatedBy = user.id
        )
    }
}
