package com.project.management.money.project.usecases

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.money.MoneyValidationService
import com.project.management.money.customer.CustomerMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.ProjectIncome
import com.project.management.projects.ProjectIncomeRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class DeleteProjectIncomeMoneyUseCase(
    private val currentUser: CurrentUserConfig,
    private val customer: CustomerMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService,
    private val money: MoneyValidationService
) {

    @Transactional
    fun delete(projectIncomeId: Long) {
        val user = currentUser.getCurrentUser()

        // Get existing income for deletion
        val existingIncome = ProjectIncomeRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = projectIncomeId
        ).validate()

        projectScenarios(existingIncome)
        organizationScenario(existingIncome)

        // Delete customer transaction
        transactionScenario(existingIncome)

        // Delete project income
        ProjectIncomeRepository.Mutate.deleteById(
            id = projectIncomeId,
            organizationId = OrganizationId(user.organizationId),
            updatedBy = user.id!!
        )

        money.validate(organizationId = user.organizationId)
    }

    private fun transactionScenario(income: ProjectIncome) {
        customer.projectIncomeDelete(
            customerId = income.customerId,
            transactionId = income.customerTransaction.id!!,
            amount = income.customerTransaction.amount,
            amountPaid = income.customerTransaction.amountPaid
        )
    }

    private fun organizationScenario(income: ProjectIncome) {
        organizationProjectsMoneyMutateService.decreaseIncomes(
            amount = income.customerTransaction.amount,
            amountPaid = income.customerTransaction.amountPaid
        )
    }

    private fun projectScenarios(income: ProjectIncome) {
        projectMoneyMutateService.projectIncomeDelete(income = income)
    }
}
