package com.project.management.money.project

import com.project.management.customers.PostRequestProjectIncomePay
import com.project.management.money.project.usecases.ChangeProjectExpenseBeneficiaryUseCase
import com.project.management.money.project.usecases.ChangeProjectExpenseUseCase
import com.project.management.money.project.usecases.CreateProjectExpenseUseCase
import com.project.management.money.project.usecases.CreateProjectIncomeUseCase
import com.project.management.money.project.usecases.DeleteProjectExpenseMoneyUseCase
import com.project.management.money.project.usecases.DeleteProjectIncomeMoneyUseCase
import com.project.management.money.project.usecases.UpdateProjectExpenseMoneyUseCase
import com.project.management.money.project.usecases.UpdateProjectIncomeMoneyUseCase
import com.project.management.projects.ProjectExpense
import com.project.management.projects.ProjectIncome
import com.project.management.projects.ProjectExpenseAmountPatchRequest
import com.project.management.projects.ProjectIncomeAmountPatchRequest
import com.project.management.projects.ProjectExpensePostRequest
import com.project.management.projects.ProjectIncomePostRequest
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class ProjectMoneyMutateService(
    private val createExpense: CreateProjectExpenseUseCase,
    private val updateExpense: UpdateProjectExpenseMoneyUseCase,
    private val deleteExpense: DeleteProjectExpenseMoneyUseCase,
    private val changeExpenseProject: ChangeProjectExpenseUseCase,
    private val changeExpenseBeneficiary: ChangeProjectExpenseBeneficiaryUseCase,
    private val createIncome: CreateProjectIncomeUseCase,
    private val updateIncome: UpdateProjectIncomeMoneyUseCase,
    private val deleteIncome: DeleteProjectIncomeMoneyUseCase
) {

    @Transactional
    fun projectExpenseAdd(request: ProjectExpensePostRequest, projectId: Long): ProjectExpense {
        return createExpense.create(request, projectId)
    }

    @Transactional
    fun projectExpenseUpdate(request: ProjectExpenseAmountPatchRequest, projectExpenseId: Long): ProjectExpense {
        return updateExpense.update(request, projectExpenseId)
    }

    @Transactional
    fun projectExpenseDelete(projectExpenseId: Long) {
        deleteExpense.delete(projectExpenseId)
    }

    @Transactional
    fun projectExpenseChangeProject(expenseId: Long, oldProjectId: Long, newProjectId: Long) {
        changeExpenseProject.changeProject(
            expenseId = expenseId,
            oldProjectId = oldProjectId,
            newProjectId = newProjectId
        )
    }

    @Transactional
    fun projectExpenseChangeBeneficiary(
        expenseId: Long,
        oldBeneficiaryId: Long?,
        newBeneficiaryId: Long?
    ) {
        changeExpenseBeneficiary.changeBeneficiary(
            expenseId = expenseId,
            oldBeneficiaryId = oldBeneficiaryId,
            newBeneficiaryId = newBeneficiaryId
        )
    }

    @Transactional
    fun projectIncomeAdd(request: ProjectIncomePostRequest, projectId: Long): ProjectIncome {
        return createIncome.create(request, projectId)
    }

    @Transactional
    fun projectIncomeDelete(projectIncomeId: Long) {
        deleteIncome.delete(projectIncomeId)
    }

    @Transactional
    fun projectIncomeUpdate(request: ProjectIncomeAmountPatchRequest, projectIncomeId: Long): ProjectIncome {
        return updateIncome.update(request, projectIncomeId)
    }

    @Transactional
    fun projectIncomePay(request: PostRequestProjectIncomePay, customerTransactionId: Long): ProjectIncome {
        return updateIncome.pay(request, customerTransactionId)
    }
}