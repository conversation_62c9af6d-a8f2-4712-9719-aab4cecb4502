package com.project.management.money.project.usecases

import com.project.management.beneficiaries.BeneficiaryRepository
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.user.UserMoneyMutateService

import com.project.management.money.beneficiary.BeneficiaryMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.ProjectExpense
import com.project.management.projects.ProjectExpenseAmountPatchRequest
import com.project.management.projects.ProjectExpenseRepository
import com.project.management.projects.ProjectRepository
import com.project.management.users.User
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateProjectExpenseMoneyUseCase(
    private val currentUser: CurrentUserConfig,
    private val beneficiary: BeneficiaryMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val userMoneyMutateService: UserMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService
) {

    @Transactional
    fun update(request: ProjectExpenseAmountPatchRequest, projectExpenseId: Long): ProjectExpense {
        val user = currentUser.getCurrentUser()

        // Get existing expense for comparison
        val expense = ProjectExpenseRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = projectExpenseId
        ).validate()

        validate(request, user, expense)
        userScenario(expense = expense, request = request)
        projectScenarios(request, expense)
        organizationScenario(expense = expense, request = request)

        transactionScenario(request, expense)

        return expense
    }

    private fun userScenario(expense: ProjectExpense, request: ProjectExpenseAmountPatchRequest) {
        userMoneyMutateService.projectExpenseModify(request = request, expense = expense)
    }

    private fun transactionScenario(
        request: ProjectExpenseAmountPatchRequest,
        existingExpense: ProjectExpense
    ) {
        beneficiary.projectExpenseModify(
            transaction = existingExpense.beneficiaryTransaction,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun organizationScenario(expense: ProjectExpense, request: ProjectExpenseAmountPatchRequest) {
        organizationProjectsMoneyMutateService.modifyExpenses(
            oldAmount = expense.beneficiaryTransaction.amount,
            oldAmountPaid = expense.beneficiaryTransaction.amountPaid,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun projectScenarios(
        request: ProjectExpenseAmountPatchRequest,
        existingExpense: ProjectExpense
    ) {
        projectMoneyMutateService.projectExpenseModify(
            projectId = existingExpense.projectId,
            oldAmount = existingExpense.beneficiaryTransaction.amount,
            oldAmountPaid = existingExpense.beneficiaryTransaction.amountPaid,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun validate(
        request: ProjectExpenseAmountPatchRequest,
        user: User,
        existingExpense: ProjectExpense
    ) {
        // Check admin permission
        if (!user.isAdmin) {
            throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        if (existingExpense.beneficiaryTransaction.version != request.transactionVersion) {
            throw BusinessException.ConflictException(message = "Transaction version conflict.")
        }

        // Validate amount constraints
        if (request.amountPaid > request.amount) {
            throw BusinessException.BadRequestException(
                message = "Amount paid cannot be greater than amount."
            )
        }

        // Validate project exists
        ProjectRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = existingExpense.projectId
        ).validate()

        // Validate beneficiary exists if provided
        if (existingExpense.beneficiaryId == null) return

        BeneficiaryRepository.Query
            .getById(OrganizationId(user.organizationId), existingExpense.beneficiaryId!!).validate()
    }
}
