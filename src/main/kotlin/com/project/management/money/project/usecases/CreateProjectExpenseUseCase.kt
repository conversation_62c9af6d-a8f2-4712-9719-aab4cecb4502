package com.project.management.money.project.usecases

import com.project.management.beneficiaries.BeneficiaryRepository
import com.project.management.beneficiaries.BeneficiaryTransaction
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.money.beneficiary.BeneficiaryMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.ProjectExpense
import com.project.management.projects.ProjectExpensePostRequest
import com.project.management.projects.ProjectExpenseRepository
import com.project.management.projects.ProjectRepository
import com.project.management.terms.TermRepository
import com.project.management.terms.TermsGroupRepository
import com.project.management.users.User
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateProjectExpenseUseCase(
    private val currentUser: CurrentUserConfig,
    private val beneficiary: BeneficiaryMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val userMoneyMutateService: UserMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService
) {

    @Transactional
    fun create(request: ProjectExpensePostRequest, projectId: Long): ProjectExpense {
        val user = currentUser.getCurrentUser()
        validate(request, user, projectId)

        val transaction = beneficiaryScenario(request, projectId)
        val expense = projectScenarios(request, projectId, transaction)

        userScenario(user = user, expense = expense)
        organizationScenario(expense = expense)

        return expense
    }


    private fun userScenario(user: User, expense: ProjectExpense) {
        userMoneyMutateService.projectExpenseAdd(user.id!!, expense)
    }

    private fun beneficiaryScenario(
        request: ProjectExpensePostRequest,
        projectId: Long
    ): BeneficiaryTransaction {
        return beneficiary.projectExpenseAdd(request = request, projectId = projectId)
    }

    private fun organizationScenario(expense: ProjectExpense) {
        organizationProjectsMoneyMutateService.increaseExpenses(
            amount = expense.beneficiaryTransaction.amountPaid,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
    }


    private fun projectScenarios(
        request: ProjectExpensePostRequest,
        projectId: Long,
        transaction: BeneficiaryTransaction
    ): ProjectExpense {
        return projectMoneyMutateService.projectExpenseAdd(
            request = request,
            projectId = projectId,
            transaction = transaction
        )
    }

    private fun validate(request: ProjectExpensePostRequest, user: User, project: Long) {
        // Validate project (required)
        ProjectRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = project
        ).validate()

        validateBeneficiary(request, user)

        // Validate terms consistency - both must be provided together or both must be null
        if (request.termsGroupId != null && request.termId == null) {
            throw BusinessException.BadRequestException(message = "Both termsGroupId and termId must be provided together or both must be null.")
        }

        if (request.termId != null && request.termsGroupId == null) {
            throw BusinessException.BadRequestException(message = "Both termsGroupId and termId must be provided together or both must be null.")
        }

        // Early return if no terms provided
        if (request.termsGroupId == null && request.termId == null) {
            return
        }

        // Validate terms when both are provided
        val termsGroup = TermsGroupRepository.Query
            .getById(organizationId = user, id = request.termsGroupId!!).validate()
        val term = TermRepository.Query
            .getById(organizationId = user, id = request.termId!!).validate()

        if (term.termsGroupId != termsGroup.id) {
            throw BusinessException.ConflictException(message = "Term and TermsGroup do not match.")
        }
    }

    private fun validateBeneficiary(request: ProjectExpensePostRequest, user: User) {
        // Early return if no beneficiary provided
        if (request.beneficiaryId == null) return
        BeneficiaryRepository.Query
            .getById(OrganizationId(user.organizationId), request.beneficiaryId).validate()
    }
}
