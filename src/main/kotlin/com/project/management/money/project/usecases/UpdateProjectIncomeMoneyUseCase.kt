package com.project.management.money.project.usecases

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.customers.toEntity
import com.project.management.customers.PostRequestProjectIncomePay
import com.project.management.customers.CustomerRepository
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.money.customer.CustomerMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.ProjectIncome
import com.project.management.projects.ProjectIncomeAmountPatchRequest
import com.project.management.projects.ProjectIncomeRepository
import com.project.management.projects.ProjectRepository
import com.project.management.projects.PostRequestProjectIncomePay
import com.project.management.users.User
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateProjectIncomeMoneyUseCase(
    private val currentUser: CurrentUserConfig,
    private val customer: CustomerMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService
) {

    fun pay(request: PostRequestProjectIncomePay, customerTransactionId: Long): ProjectIncome {
        val income = ProjectIncomeRepository.Query.getByCustomerTransactionId(
            organizationId = OrganizationId(currentUser.getCurrentUser().organizationId),
            customerTransactionId = customerTransactionId
        ).validate()
        return update(
            request.toModifyAmount(income.customerTransaction.amount.toDouble()), income.id!!
        )
    }

    @Transactional
    fun update(
        request: ProjectIncomeAmountPatchRequest,
        projectIncomeId: Long
    ): ProjectIncome {
        val user = currentUser.getCurrentUser()

        // Get existing income for comparison
        val income = ProjectIncomeRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = projectIncomeId
        ).validate()

        validate(request, user, income)
        projectScenarios(request, income)
        organizationScenario(income = income, request = request)
        transactionScenario(request, income)

        return income
    }

    private fun transactionScenario(
        request: ProjectIncomeAmountPatchRequest,
        existingIncome: ProjectIncome
    ) {
        customer.projectIncomeModify(
            transaction = existingIncome.customerTransaction.toEntity(),
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun organizationScenario(
        income: ProjectIncome,
        request: ProjectIncomeAmountPatchRequest
    ) {
        organizationProjectsMoneyMutateService.modifyIncomes(
            oldAmount = income.customerTransaction.amount,
            oldAmountPaid = income.customerTransaction.amountPaid,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun projectScenarios(
        request: ProjectIncomeAmountPatchRequest,
        existingIncome: ProjectIncome
    ) {
        projectMoneyMutateService.projectIncomeModify(
            projectId = existingIncome.projectId,
            oldAmount = existingIncome.customerTransaction.amount,
            oldAmountPaid = existingIncome.customerTransaction.amountPaid,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun validate(
        request: ProjectIncomeAmountPatchRequest,
        user: User,
        existingIncome: ProjectIncome
    ) {
        // Check admin permission
        if (!user.isAdmin) {
            throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        // Check version conflicts
        if (existingIncome.customerTransaction.version != request.transactionVersion) {
            throw BusinessException.ConflictException(message = "Project income version conflict. ${existingIncome.customerTransaction.version} != ${request.transactionVersion}")
        }
    }
}
