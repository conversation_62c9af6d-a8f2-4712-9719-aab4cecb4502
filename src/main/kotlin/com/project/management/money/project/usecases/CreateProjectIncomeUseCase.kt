package com.project.management.money.project.usecases

import com.project.management.customers.CustomerTransaction
import com.project.management.customers.CustomerRepository
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException

import com.project.management.money.customer.CustomerMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.ProjectIncome
import com.project.management.projects.ProjectIncomePostRequest
import com.project.management.projects.ProjectIncomeRepository
import com.project.management.projects.ProjectRepository
import com.project.management.users.User
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateProjectIncomeUseCase(
    private val currentUser: CurrentUserConfig,
    private val customer: CustomerMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService
) {

    @Transactional
    fun create(request: ProjectIncomePostRequest, projectId: Long): ProjectIncome {
        val user = currentUser.getCurrentUser()
        validate(request, user, projectId)

        val transaction = customerScenario(request, projectId)
        val income = projectScenarios(request, projectId, transaction)

        organizationScenario(income = income)

        return income
    }

    private fun customerScenario(
        request: ProjectIncomePostRequest,
        projectId: Long
    ): CustomerTransaction {
        return customer.projectIncomeAdd(request = request, projectId = projectId)
    }

    private fun organizationScenario(income: ProjectIncome) {
        organizationProjectsMoneyMutateService.increaseIncomes(
            amount = income.customerTransaction.amount,
            amountPaid = income.customerTransaction.amountPaid
        )
    }

    private fun projectScenarios(
        request: ProjectIncomePostRequest,
        projectId: Long,
        transaction: CustomerTransaction
    ): ProjectIncome {
        return projectMoneyMutateService.projectIncomeAdd(
            request = request,
            projectId = projectId,
            transaction = transaction
        )
    }

    private fun validate(request: ProjectIncomePostRequest, user: User, projectId: Long) {
        // Validate amount constraints
        if (request.amountPaid > request.amount) {
            throw BusinessException.BadRequestException(
                message = "Amount paid cannot be greater than amount."
            )
        }

        // Validate project exists
        ProjectRepository.Query.getById(
            organizationId = OrganizationId(user.organizationId),
            id = projectId
        ).validate()

        // Validate customer exists
        CustomerRepository.Query
            .getById(user, request.customerId).validate()
    }
}
