package com.project.management.terms

import com.project.management.common.entity.ServerEntity
import com.project.management.users.User
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef

const val TERM_DELETED_FILTER = "term_deleted_filter"
const val TERMS_GROUP_DELETED_FILTER = "terms_group_deleted_filter"

@Entity
@Table(name = "terms")
@FilterDef(name = TERM_DELETED_FILTER, parameters = [])
@Filter(name = TERM_DELETED_FILTER, condition = "deleted is NULL")
class Term(
    var name: String,
    var description: String?,

    @Column(name = "terms_group_id")
    val termsGroupId: Long,

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "terms_group_id", updatable = false, insertable = false)
    val termsGroup: TermsGroup,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override var createdByUser: User
) : ServerEntity()

@Entity
@Table(name = "terms_groups")
@FilterDef(name = TERMS_GROUP_DELETED_FILTER, parameters = [])
@Filter(name = TERMS_GROUP_DELETED_FILTER, condition = "deleted is NULL")
class TermsGroup(
    var name: String,
    var description: String,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override var createdByUser: User
) : ServerEntity()
