package com.project.management.analytics.models

import com.project.management.projects.ProjectExpense
import com.project.management.projects.ProjectIncome
import java.math.BigDecimal

data class HomeSummary(
    val incomesThisWeek: BigDecimal,
    val expensesThisWeek: BigDecimal,
    val incomesLastWeek: BigDecimal,
    val expensesLastWeek: BigDecimal,
    val largestIncomes: List<ProjectIncome>,
    val largestExpenses: List<ProjectExpense>
)