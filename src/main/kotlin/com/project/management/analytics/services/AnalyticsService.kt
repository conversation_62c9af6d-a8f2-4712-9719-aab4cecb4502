package com.project.management.analytics.services

import com.project.management.analytics.models.HomeSummary
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.projects.ProjectExpenseRepository
import com.project.management.projects.ProjectIncomeRepository
import org.springframework.stereotype.Service

@Service
class AnalyticsService(
    private val currentUser: CurrentUserConfig,
) {

    fun getHomeSummary(): HomeSummary {
        val user = currentUser.getCurrentUser()
        val expensesLastWeek = ProjectExpenseRepository.Query.getAllCreatedLastWeek(user)
        val expensesThisWeek = ProjectExpenseRepository.Query.getAllCreatedThisWeek(user)

        val incomesLastWeek = ProjectIncomeRepository.Query.getAllCreatedLastWeek(user)
        val incomesThisWeek = ProjectIncomeRepository.Query.getAllCreatedThisWeek(user)

        return HomeSummary(
            incomesThisWeek = incomesThisWeek.sumOf { it.customerTransaction.amount },
            expensesThisWeek = expensesThisWeek.sumOf { it.beneficiaryTransaction.amount },
            incomesLastWeek = incomesLastWeek.sumOf { it.customerTransaction.amount },
            expensesLastWeek = expensesLastWeek.sumOf { it.beneficiaryTransaction.amount },
            largestIncomes = incomesThisWeek.sortedByDescending { it.customerTransaction.amount }.take(5),
            largestExpenses = expensesThisWeek.sortedByDescending { it.beneficiaryTransaction.amount }.take(5),
        )
    }
}