package com.project.management.customers

import com.project.management.projects.requests.PostRequestProjectIncome
import com.project.management.users.User
import java.time.ZonedDateTime

fun CustomerPostRequest.toModel(performer: User): Customer {
    return Customer(
        name = name,
        phoneNumber = phoneNumber,
        secondaryPhoneNumber = secondaryPhoneNumber,
        balanceAccumulator = 0.0.toBigDecimal(),
        paidAccumulator = 0.0.toBigDecimal(),
        organizationId = performer.organizationId,
        createdBy = performer.id!!,
        updatedBy = performer.id!!,
        createdByUser = performer
    )
}

fun PostRequestProjectIncome.toCustomerTransaction(projectId: Long): CustomerTransactionPostRequest {
    return CustomerTransactionPostRequest(
        amount = amount,
        amountPaid = amountPaid,
        description = description,
        projectId = projectId,
        transactionDate = transactionDate
    )
}

fun CustomerTransactionPostRequest.toEntity(
    customerId: Long,
    user: User,
): CustomerTransaction {
    return CustomerTransaction(
        organizationId = user.organizationId,
        amount = amount.toBigDecimal(),
        amountPaid = amountPaid.toBigDecimal(),
        description = description,
        customerId = customerId,
        projectId = projectId,
        transactionDate = ZonedDateTime.parse(transactionDate),
        createdBy = user.id!!,
        updatedBy = user.id!!,
        createdByUser = user
    )
}
