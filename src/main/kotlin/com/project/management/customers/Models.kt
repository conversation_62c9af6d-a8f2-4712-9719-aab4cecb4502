package com.project.management.customers

import com.project.management.common.entity.ServerEntity
import com.project.management.users.User
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import org.hibernate.annotations.SQLRestriction
import java.math.BigDecimal
import java.time.ZonedDateTime

const val CUSTOMER_DELETED_FILTER = "customer_deleted_filter"

@Entity
@Table(name = "customers")
@FilterDef(name = CUSTOMER_DELETED_FILTER, parameters = [])
@Filter(name = CUSTOMER_DELETED_FILTER, condition = "deleted is NULL")
class Customer(
    var name: String,
    var phoneNumber: String,
    var secondaryPhoneNumber: String?,

    var balanceAccumulator: BigDecimal,
    var paidAccumulator: BigDecimal,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override var createdByUser: User
) : ServerEntity()

const val CUSTOMER_TRANSACTION_DELETED_FILTER = "customer_transaction_deleted_filter"

@SQLRestriction("deleted IS NULL")
@Entity
@Table(name = "customer_transactions")
@FilterDef(name = CUSTOMER_TRANSACTION_DELETED_FILTER, parameters = [])
@Filter(name = CUSTOMER_TRANSACTION_DELETED_FILTER, condition = "deleted is NULL")
class CustomerTransaction(
    var amount: BigDecimal,
    var amountPaid: BigDecimal,
    var description: String,
    val customerId: Long,
    val projectId: Long,
    var transactionDate: ZonedDateTime,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
    override var createdByUser: User
) : ServerEntity()

fun CustomerTransaction.toEntity(): CustomerTransaction {
    return this
}

